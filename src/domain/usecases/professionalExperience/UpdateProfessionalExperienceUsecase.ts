import { IUpdateProfessionalExperienceRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { IUpdateProfessionalExperienceUsecase } from "@/domain/interfaces/usecases/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";

class UpdateProfessionalExperienceUsecase implements IUpdateProfessionalExperienceUsecase {
  constructor(
    private readonly updateProfessionalExperienceRepository: IUpdateProfessionalExperienceRepository
  ) {}

  async execute(id: number, experience: Partial<ExperienceProfessionnel>): Promise<ExperienceProfessionnel | null> {
    try {
      return await this.updateProfessionalExperienceRepository.execute(id, experience);
    } catch (error) {
      console.error("Error updating professional experience:", error);
      return null;
    }
  }
}

export default UpdateProfessionalExperienceUsecase;