import { ICreateProfessionalExperienceRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { ICreateProfessionalExperienceUsecase } from "@/domain/interfaces/usecases/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";

class CreateProfessionalExperienceUsecase
  implements ICreateProfessionalExperienceUsecase {
  constructor(
    private readonly createProfessionalExperienceRepository:
      ICreateProfessionalExperienceRepository,
  ) { }

  async execute(
    experience: Omit<ExperienceProfessionnel, "id">[],
  ): Promise<ExperienceProfessionnel[]> {
    try {
      const experienciesWithoutId: Omit<ExperienceProfessionnel, "id">[] = experience.map((exp) => {
        return {
          poste: exp.poste,
          date_debut: exp.date_debut,
          date_fin: exp.date_fin,
          description: exp.description,
          etablissement: exp.etablissement,
          id_professionnel: exp.id_professionnel,
          est_actuel: exp.est_actuel,
        }
      });
      return await this.createProfessionalExperienceRepository.execute(
        experienciesWithoutId,
      );
    } catch (error) {
      console.error("Error creating professional experience:", error);
      return null;
    }
  }
}

export default CreateProfessionalExperienceUsecase;
