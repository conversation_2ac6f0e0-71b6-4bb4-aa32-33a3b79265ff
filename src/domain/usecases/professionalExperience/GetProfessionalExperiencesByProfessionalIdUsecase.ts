import { IGetProfessionalExperiencesByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { IGetProfessionalExperiencesByProfessionalIdUsecase } from "@/domain/interfaces/usecases/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";

class GetProfessionalExperiencesByProfessionalIdUsecase implements IGetProfessionalExperiencesByProfessionalIdUsecase {
  constructor(
    private readonly getProfessionalExperiencesByProfessionalIdRepository: IGetProfessionalExperiencesByProfessionalIdRepository
  ) {}

  async execute(professionalId: number): Promise<ExperienceProfessionnel[] | null> {
    try {
      return await this.getProfessionalExperiencesByProfessionalIdRepository.execute(professionalId);
    } catch (error) {
      console.error("Error getting professional experiences by professional ID:", error);
      return null;
    }
  }
}

export default GetProfessionalExperiencesByProfessionalIdUsecase;