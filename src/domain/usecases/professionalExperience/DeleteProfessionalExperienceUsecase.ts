import { IDeleteProfessionalExperienceRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { IDeleteProfessionalExperienceUsecase } from "@/domain/interfaces/usecases/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";

class DeleteProfessionalExperienceUsecase implements IDeleteProfessionalExperienceUsecase {
  constructor(
    private readonly deleteProfessionalExperienceRepository: IDeleteProfessionalExperienceRepository
  ) {}

  async execute(id: number): Promise<ExperienceProfessionnel | null> {
    try {
      return await this.deleteProfessionalExperienceRepository.execute(id);
    } catch (error) {
      console.error("Error deleting professional experience:", error);
      return null;
    }
  }
}

export default DeleteProfessionalExperienceUsecase;