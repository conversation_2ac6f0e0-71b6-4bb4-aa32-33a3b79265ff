import { IDeleteLanguageRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { IDeleteLanguageUsecase } from "@/domain/interfaces/usecases/professionalLanguage";

class DeleteLanguageUsecase implements IDeleteLanguageUsecase {
  constructor(
    private readonly deleteLanguageRepository: IDeleteLanguageRepository
  ) {}

  async execute(id: number): Promise<void> {
    try {
      await this.deleteLanguageRepository.execute(id);
    } catch (error) {
      console.error("Error deleting language:", error);
      throw error;
    }
  }
}

export default DeleteLanguageUsecase;