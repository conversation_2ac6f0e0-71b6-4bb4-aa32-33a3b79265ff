import { IGetLanguagesByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { IGetLanguagesByProfessionalIdUsecase } from "@/domain/interfaces/usecases/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";

class GetLanguagesByProfessionalIdUsecase implements IGetLanguagesByProfessionalIdUsecase {
  constructor(
    private readonly getLanguagesByProfessionalIdRepository: IGetLanguagesByProfessionalIdRepository
  ) {}

  async execute(professionalId: number): Promise<LangueParleeProfessionnel[]> {
    try {
      return await this.getLanguagesByProfessionalIdRepository.execute(professionalId);
    } catch (error) {
      console.error("Error getting languages by professional ID:", error);
      throw error;
    }
  }
}

export default GetLanguagesByProfessionalIdUsecase;