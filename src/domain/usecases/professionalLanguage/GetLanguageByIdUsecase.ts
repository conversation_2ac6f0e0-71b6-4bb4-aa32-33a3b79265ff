import { IGetLanguageByIdRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { IGetLanguageByIdUsecase } from "@/domain/interfaces/usecases/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";

class GetLanguageByIdUsecase implements IGetLanguageByIdUsecase {
  constructor(
    private readonly getLanguageByIdRepository: IGetLanguageByIdRepository
  ) {}

  async execute(id: number): Promise<LangueParleeProfessionnel> {
    try {
      return await this.getLanguageByIdRepository.execute(id);
    } catch (error) {
      console.error("Error getting language by ID:", error);
      throw error;
    }
  }
}

export default GetLanguageByIdUsecase;