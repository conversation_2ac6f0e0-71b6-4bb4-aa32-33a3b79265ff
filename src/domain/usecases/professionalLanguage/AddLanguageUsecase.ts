import { IAddLanguageRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { IAddLanguageUsecase } from "@/domain/interfaces/usecases/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";

class AddLanguageUsecase implements IAddLanguageUsecase {
  constructor(
    private readonly addLanguageRepository: IAddLanguageRepository
  ) { }

  async execute(language: Omit<LangueParleeProfessionnel, "id">[]): Promise<LangueParleeProfessionnel[]> {
    try {
      return await this.addLanguageRepository.execute(language);
    } catch (error) {
      console.error("Error adding language:", error);
      throw error;
    }
  }
}

export default AddLanguageUsecase;
