import { IGetProfessionalDiplomaByIdRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { IGetProfessionalDiplomaByIdUsecase } from "@/domain/interfaces/usecases/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";

class GetProfessionalDiplomaByIdUsecase implements IGetProfessionalDiplomaByIdUsecase {
  constructor(
    private readonly getProfessionalDiplomaByIdRepository: IGetProfessionalDiplomaByIdRepository
  ) {}

  async execute(id: number): Promise<DiplomeProfessionnel | null> {
    try {
      return await this.getProfessionalDiplomaByIdRepository.execute(id);
    } catch (error) {
      console.error("Error getting professional diploma by ID:", error);
      return null;
    }
  }
}

export default GetProfessionalDiplomaByIdUsecase;