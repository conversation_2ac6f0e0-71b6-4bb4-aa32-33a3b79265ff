import { IUpdateProfessionalDiplomaRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { IUpdateProfessionalDiplomaUsecase } from "@/domain/interfaces/usecases/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";

class UpdateProfessionalDiplomaUsecase implements IUpdateProfessionalDiplomaUsecase {
  constructor(
    private readonly updateProfessionalDiplomaRepository: IUpdateProfessionalDiplomaRepository
  ) {}

  async execute(id: number, diploma: Partial<DiplomeProfessionnel>): Promise<DiplomeProfessionnel | null> {
    try {
      return await this.updateProfessionalDiplomaRepository.execute(id, diploma);
    } catch (error) {
      console.error("Error updating professional diploma:", error);
      return null;
    }
  }
}

export default UpdateProfessionalDiplomaUsecase;