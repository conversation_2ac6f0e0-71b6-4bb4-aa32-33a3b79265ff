import { IGetProfessionalDiplomasByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { IGetProfessionalDiplomasByProfessionalIdUsecase } from "@/domain/interfaces/usecases/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";

class GetProfessionalDiplomasByProfessionalIdUsecase implements IGetProfessionalDiplomasByProfessionalIdUsecase {
  constructor(
    private readonly getProfessionalDiplomasByProfessionalIdRepository: IGetProfessionalDiplomasByProfessionalIdRepository
  ) {}

  async execute(professionalId: number): Promise<DiplomeProfessionnel[] | null> {
    try {
      return await this.getProfessionalDiplomasByProfessionalIdRepository.execute(professionalId);
    } catch (error) {
      console.error("Error getting professional diplomas by professional ID:", error);
      return null;
    }
  }
}

export default GetProfessionalDiplomasByProfessionalIdUsecase;