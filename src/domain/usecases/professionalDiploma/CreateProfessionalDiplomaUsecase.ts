import { ICreateProfessionalDiplomaRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { ICreateProfessionalDiplomaUsecase } from "@/domain/interfaces/usecases/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";

class CreateProfessionalDiplomaUsecase implements ICreateProfessionalDiplomaUsecase {
  constructor(
    private readonly createProfessionalDiplomaRepository: ICreateProfessionalDiplomaRepository
  ) { }

  async execute(diploma: Omit<DiplomeProfessionnel, "id">[]): Promise<DiplomeProfessionnel[]> {
    try {
      const diplomaWithoutId: Omit<DiplomeProfessionnel, "id">[] = diploma.map((d) => {
        return {
          titre: d.titre,
          description: d.description,
          etablissement: d.etablissement,
          annee: d.annee,
          id_professionnel: d.id_professionnel
        };
      });

      return await this.createProfessionalDiplomaRepository.execute(diplomaWithoutId);
    } catch (error) {
      console.error("Error creating professional diploma:", error);
      return null;
    }
  }
}

export default CreateProfessionalDiplomaUsecase;
