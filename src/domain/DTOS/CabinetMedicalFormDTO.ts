import {
  Diplome,
  Experience,
  Langue,
  PaymentMethod,
  Publication,
} from "@/presentation/hooks/cabinetMedical/use-cabinet-medical-form.ts";
import {
  Commune,
  District,
  ListeAssurances,
  ListeSpecialites,
  ordre_appartenance,
  Region,
} from "../models";
import { professionnels_titre_enum } from "../models/enums/professionnelsTitreEnum.ts";
import { professionnels_types_consultation_enum } from "../models/enums/professionnelTypeConsultation.ts";
import { sexe_enum } from "../models/enums/sexe.ts";
import { mot_cles } from "../models/MotCles.ts";

export interface CabinetMedicalFormDTO {
  titre: professionnels_titre_enum;
  nom: string;
  prenom: string;
  sexe: sexe_enum;
  numero_ordre: string;
  raison_sociale: string;
  nif: string;
  stat: string;
  ordre_appartenance: ordre_appartenance[];
  specialities: ListeSpecialites[];
  // Champs pour l'établissement professionnel
  nom_etablissement: string;
  nom_responsable: string;
  prenom_responsable: string;
  equipe: string;
  adresse: string;
  region: Region | null;
  district: District | null;
  commune: Commune | null;
  fokotany: string;
  infoAcces: string;
  email: string;
  telephone: string;
  presentation: string;
  profileImageFile: File; // Fichier d'image de profil
  profileImagePreviewUrl?: string; // URL pour la prévisualisation de l'image de profil (non envoyée au serveur)
  cabinetImages: File[]; // Fichiers d'images du cabinet
  cabinetImagesPreviewUrls?: string[]; // URLs pour la prévisualisation des images (non envoyées au serveur)
  cabinetImagesErrors?: string[]; // Erreurs liées aux images du cabinet
  typeConsultation: professionnels_types_consultation_enum;
  nouveauPatientAcceptes: boolean;
  paymentMethods: PaymentMethod[];
  insurances?: ListeAssurances[];
  motCles?: mot_cles[];
  diplomes?: Omit<Diplome, "id">[];
  experiences?: Omit<Experience, "id">[];
  publications?: Omit<Publication, "id">[];
  langues?: Omit<Langue, "id">[];
  geolocation: string;
  motDePasse: string;
  confirmMotDePasse: string;
}
