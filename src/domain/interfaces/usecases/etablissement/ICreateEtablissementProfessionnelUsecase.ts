import { EtablissementProfessionnel } from "@/domain/models";

/**
 * Interface pour le use case de création d'établissement professionnel
 * 
 * @description Définit le contrat pour la création d'un établissement professionnel
 */
export interface ICreateEtablissementProfessionnelUsecase {
  /**
   * Exécute la création d'un établissement professionnel
   * 
   * @param etablissement - Les données de l'établissement à créer (sans l'ID)
   * @returns Promise<EtablissementProfessionnel> - L'établissement créé avec son ID
   * @throws Error si la création échoue
   */
  execute(
    etablissement: Omit<EtablissementProfessionnel, "id">
  ): Promise<EtablissementProfessionnel>;
}
