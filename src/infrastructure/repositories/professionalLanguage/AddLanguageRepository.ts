import { IAddLanguageRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_LANGUAGE_TABLE_NAME } from "./constants";

export class AddLanguageRepository implements IAddLanguageRepository {
  async execute(
    language: Omit<LangueParleeProfessionnel, "id">[],
  ): Promise<LangueParleeProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_LANGUAGE_TABLE_NAME)
      .insert(language)
      .select()

    if (error) throw error;

    return data as LangueParleeProfessionnel[];
  }
}
