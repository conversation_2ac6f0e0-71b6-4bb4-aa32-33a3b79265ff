import { IGetLanguageByIdRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_LANGUAGE_TABLE_NAME } from "./constants";

export class GetLanguageByIdRepository implements IGetLanguageByIdRepository {
  async execute(id: number): Promise<LangueParleeProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_LANGUAGE_TABLE_NAME)
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return data;
  }
}
