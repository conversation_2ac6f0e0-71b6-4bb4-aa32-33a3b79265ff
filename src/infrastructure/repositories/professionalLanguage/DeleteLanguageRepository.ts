import { IDeleteLanguageRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_LANGUAGE_TABLE_NAME } from "./constants";

export class DeleteLanguageRepository implements IDeleteLanguageRepository {
  async execute(id: number): Promise<void> {
    const { error } = await supabase
      .from(PROFESSIONAL_LANGUAGE_TABLE_NAME)
      .delete()
      .eq("id", id);

    if (error) throw error;
  }
}
