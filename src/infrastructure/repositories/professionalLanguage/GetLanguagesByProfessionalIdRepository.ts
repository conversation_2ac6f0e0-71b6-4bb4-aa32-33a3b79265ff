import { IGetLanguagesByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalLanguage";
import { LangueParleeProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_LANGUAGE_TABLE_NAME } from "./constants";

export class GetLanguagesByProfessionalIdRepository
  implements IGetLanguagesByProfessionalIdRepository {
  async execute(professionalId: number): Promise<LangueParleeProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_LANGUAGE_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId)

      if(error) throw error

      if(!data || data.length === 0) return []

      return data;
  }
}
