import { ICreateProfessionalExperienceRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_EXPERIENCE_TABLE_NAME } from "./constants";

export class CreateProfessionalExperienceRepository
  implements ICreateProfessionalExperienceRepository {
  async execute(
    experience: Omit<ExperienceProfessionnel, "id">[],
  ): Promise<ExperienceProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_EXPERIENCE_TABLE_NAME)
      .insert(experience)
      .select();

    if (error) throw error;

    return data as ExperienceProfessionnel[];
  }
}
