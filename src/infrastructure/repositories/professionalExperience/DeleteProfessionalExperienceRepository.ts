import { IDeleteProfessionalExperienceRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONAL_EXPERIENCE_TABLE_NAME } from "./constants";

export class DeleteProfessionalExperienceRepository implements IDeleteProfessionalExperienceRepository {
  async execute(id: number): Promise<ExperienceProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_EXPERIENCE_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

      if(error) throw error

    return data as ExperienceProfessionnel;
  }
}