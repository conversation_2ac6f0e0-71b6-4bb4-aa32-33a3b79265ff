import { IGetProfessionalExperiencesByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_EXPERIENCE_TABLE_NAME } from "./constants";

export class GetProfessionalExperiencesByProfessionalIdRepository implements IGetProfessionalExperiencesByProfessionalIdRepository {
  async execute(professionalId: number): Promise<ExperienceProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_EXPERIENCE_TABLE_NAME)
      .select("*")
      .eq("id_professionnel", professionalId)
      .order("date_debut", { ascending: false })

      if(error) throw error

      if(!data || data.length === 0) return []

      return data as ExperienceProfessionnel[];
  }
}