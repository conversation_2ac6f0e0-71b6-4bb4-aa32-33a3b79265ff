import { IUpdateProfessionalExperienceRepository } from "@/domain/interfaces/repositories/professionalExperience";
import { ExperienceProfessionnel } from "@/domain/models";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_EXPERIENCE_TABLE_NAME } from "./constants";

export class UpdateProfessionalExperienceRepository implements IUpdateProfessionalExperienceRepository {
  async execute(id: number, experience: Partial<ExperienceProfessionnel>): Promise<ExperienceProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_EXPERIENCE_TABLE_NAME)
      .update(experience)
      .eq("id", id)
      .select()
      .single();

      if(error) throw error

    return data as ExperienceProfessionnel;
  }
}