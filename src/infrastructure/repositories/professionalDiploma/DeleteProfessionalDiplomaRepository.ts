import { IDeleteProfessionalDiplomaRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONAL_DIPLOMA_TABLE_NAME } from "./constants";

export class DeleteProfessionalDiplomaRepository implements IDeleteProfessionalDiplomaRepository {
  async execute(id: number): Promise<DiplomeProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_DIPLOMA_TABLE_NAME)
      .delete()
      .eq("id", id)
      .select()
      .single();

    handleError(error);

    return data as DiplomeProfessionnel;
  }
}