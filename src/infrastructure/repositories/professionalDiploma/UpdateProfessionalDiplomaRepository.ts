import { IUpdateProfessionalDiplomaRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_DIPLOMA_TABLE_NAME } from "./constants";

export class UpdateProfessionalDiplomaRepository implements IUpdateProfessionalDiplomaRepository {
  async execute(id: number, diploma: Partial<DiplomeProfessionnel>): Promise<DiplomeProfessionnel> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_DIPLOMA_TABLE_NAME)
      .update(diploma)
      .eq("id", id)
      .select()
      .single();

      if(error) throw error

    return data as DiplomeProfessionnel;
  }
}