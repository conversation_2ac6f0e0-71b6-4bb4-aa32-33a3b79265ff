import { IGetProfessionalDiplomasByProfessionalIdRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { PROFESSIONAL_DIPLOMA_TABLE_NAME } from "./constants";

export class GetProfessionalDiplomasByProfessionalIdRepository implements IGetProfessionalDiplomasByProfessionalIdRepository {
  async execute(professionalId: number): Promise<DiplomeProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_DIPLOMA_TABLE_NAME)
      .select("*")      
      .eq("id_professionnel", professionalId)
      .order("annee", { ascending: false })
      
      if(error) throw error
      
      if(!data || data.length === 0) return []

      return data as DiplomeProfessionnel[];
  }
}