import { ICreateProfessionalDiplomaRepository } from "@/domain/interfaces/repositories/professionalDiploma";
import { DiplomeProfessionnel } from "@/domain/models/DiplomeProfessionnel";
import { supabase } from "@/infrastructure/supabase/supabase";
import { handleError } from "@/infrastructure/supabase/supabaseFetchError";
import { PROFESSIONAL_DIPLOMA_TABLE_NAME } from "./constants";

export class CreateProfessionalDiplomaRepository implements ICreateProfessionalDiplomaRepository {
  async execute(diploma: Omit<DiplomeProfessionnel, "id">[]): Promise<DiplomeProfessionnel[]> {
    const { data, error } = await supabase
      .from(PROFESSIONAL_DIPLOMA_TABLE_NAME)
      .insert(diploma)
      .select()

    handleError(error);

    return data as DiplomeProfessionnel[];
  }
}
