import { useState } from "react";
import { Facturation } from "@/domain/models";

export const useFacturationForm = (
  professionalId?: number,
  patientId?: number
) => {
  const [montant, setMontant] = useState<number | null>(null);
  const [totalPaye, setTotalPaye] = useState<number | null>(null);
  const [recu, setRecu] = useState("");
  const [informations, setInformations] = useState("");
  const [datePaiement, setDatePaiement] = useState<Date>(null);

  const isFormValid = () => {
    return montant && recu && totalPaye && informations && datePaiement;
  };

  const initialiseState = (facturation: Facturation) => {
    setMontant(facturation.montant);
    setTotalPaye(facturation.total_paye);
    setRecu(facturation.recu);
    setInformations(facturation.informations);
    // setDatePaiement(facturation.date_paiement);
  };

  const getFacturationData = (): Omit<Facturation, "id"> => ({
    id_professionnel: professionalId,
    id_patient: patientId,
    montant: montant,
    total_paye: totalPaye,
    recu: recu,
    informations: informations,
    date_paiement: datePaiement,
    date_creation: new Date(),
  });

  const resetForm = () => {
    setMontant(null);
    setTotalPaye(null);
    setRecu("");
    setInformations("");
    setDatePaiement(null);
  };

  return {
    formData: {
      montant,
      totalPaye,
      recu,
      informations,
      datePaiement,
      setMontant,
      setTotalPaye,
      setRecu,
      setInformations,
      setDatePaiement,
    },
    isFormValid: isFormValid(),
    getFacturationData,
    resetForm,
    initialiseState,
  };
};
