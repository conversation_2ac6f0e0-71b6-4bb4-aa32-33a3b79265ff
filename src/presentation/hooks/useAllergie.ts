import { Allergie } from "@/domain/models";
import { AppDispatch, RootState } from "@/store";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createAllergieSlice,
  getAllAllergieSlices,
  updateAllergieSlice,
  deleteAllergieSlice,
  setSelectedAllergieSlice,
  setSelectedReactions,
  setRemarks,
  resetAllergieState,
  clearSelectedAllergieSlice,
} from "@/application/slices/professionnal/allergieSlice";

export const useAllergie = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    allergies,
    allergieState,
    selectedAllergieSlice,
    loading,
    error
  } = useSelector(
    (state: RootState) => state.allergies
  );

  const create = useCallback(
    async (data: Omit<Allergie, "id">[]) => {
      const register = await dispatch(createAllergieSlice(data));
      const allergies = register.payload as Allergie[];
      return allergies.map((allergie) => {
        return {
          id: allergie.id,
          detail: allergie.nom
        }
      });
    },
    [dispatch]
  );

  const getAll = useCallback(
    async (carnetId: number) => {
      await dispatch(getAllAllergieSlices(carnetId));
    },
    [dispatch]
  );

  const update = useCallback(
    async (id: number, data: Partial<Allergie>) => {
      await dispatch(updateAllergieSlice({ id, data }));
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteAllergieSlice(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (allergie: Allergie | null) => {
      dispatch(setSelectedAllergieSlice(allergie));
    },
    [dispatch]
  );

  const handleReactionsChange = (
    item: string,
    reaction: string,
    checked: boolean
  ) => {
    dispatch(setSelectedReactions({ item, reaction, checked }));
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };
  
  const resetState = () => {
    dispatch(resetAllergieState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedAllergieSlice());
  }, [dispatch]);

  return {
    loading,
    error,
    allergies,
    selectedAllergieSlice,
    allergieState,
    create,
    select,
    getAll,
    update,
    remove,
    clearSelected,
    handleReactionsChange,
    handleRemarksChange,
    resetState,
  };
};
