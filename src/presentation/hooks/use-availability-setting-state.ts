import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  setSchedule,
  setAvailabilityType,
  setDateSchedule,
  setDateException,
  setDureePause,
  setMaxReservationsPerDay,
  setCanInviteOthers,
  setStartDate,
  setEndDate,
  setPlanningType,
  setDuration,
  setIsMaxDays,
  setIsMinHours,
  setIsBreak,
  setIsMaxReservationsPerDay,
  setMaxDays,
  setMinHours,
  setBreakDuration,
  setSettingsLocal,
  initializeSettings,
  setErrors,
  updateSettings
} from '@/application/slices/statesInComponent/availabilitySettignsStateSlice';
import { calculateNextTimeSlot } from '@/shared/utils/calculateNextTimeSlot';
import { controle_parametre, creneau_horaire, horaire_date_specifique, horaire_hebdomadaire } from '@/domain/models';
import { AvailabilitySettingsDTO } from '@/domain/DTOS';
import { validateField } from '@/shared/utils/validateField';
import getDateFromDay from '@/shared/utils/getDateFromDay';

export const useAvailabilitySettingState = () => {
  const dispatch = useDispatch();
  const { settings, settingsLocal, errors } = useSelector((state: RootState) => state.availabilityState);

  const checkSpecifique = (updateDate: (prevDate: horaire_date_specifique[]) => horaire_date_specifique[]) => {
    if (settings.type === "specifique") {
      const newDateSchedule = updateDate(settings.horaire_date_specifique)
      dispatch(setDateSchedule(newDateSchedule))
    } else {
      const newDateException = updateDate(settings.horaire_date_specifique)
      dispatch(setDateException(newDateException))
    }
  }

  const handleAddTimeSlot = (dayIndex: number) => {
    const updateSchedule = (prevSchedule: horaire_hebdomadaire[]) => {
      const newSchedule: horaire_hebdomadaire[] = [...prevSchedule]
      const newTimeSlot = calculateNextTimeSlot(newSchedule[dayIndex].creneau_horaire)

      newSchedule[dayIndex] = {
        ...newSchedule[dayIndex],
        creneau_horaire: [...newSchedule[dayIndex].creneau_horaire, newTimeSlot]
      }
      return newSchedule
    };
    const newSchedule = updateSchedule(settings.horaire_hebdomadaire)
    dispatch(setSchedule(newSchedule))
  };
  const handleDeleteTimeSlot = (dayIndex: number, timeSlotIndex: number) => {
    const updateSchedule = (prevSchedule: horaire_hebdomadaire[]) => {
      const newSchedule: horaire_hebdomadaire[] = [...prevSchedule]
      const daySchedule = { ...newSchedule[dayIndex] }
      daySchedule.creneau_horaire = daySchedule.creneau_horaire.filter(
        (_, index) => index !== timeSlotIndex
      )
      newSchedule[dayIndex] = daySchedule
      return newSchedule
    };
    const newSchedule = updateSchedule(settings.horaire_hebdomadaire)
    dispatch(setSchedule(newSchedule))
  }
  const handleCopyTimeSlot = (sourceDayIndex: number) => {
    // Get the time slots to copy from the source day
    const sourcecreneau_horaire = settings.horaire_hebdomadaire[sourceDayIndex].creneau_horaire
    if (sourcecreneau_horaire.length === 0) return

    const updateSchedule = (prevSchedule: horaire_hebdomadaire[]) => {
      const newSchedule: horaire_hebdomadaire[] = [...prevSchedule]
      // Copy to all other days
      for (let i = 0; i < 7; i++) {
        if (i !== sourceDayIndex) {
          newSchedule[i] = {
            ...newSchedule[i],
            creneau_horaire: sourcecreneau_horaire.map((slot) => ({
              heure_debut: slot.heure_debut,
              heure_fin: slot.heure_fin
            }))
          }
        }
      }
      return newSchedule
    };
    // Create a new schedule object with the copied slots
    const newSchedule = updateSchedule(settings.horaire_hebdomadaire)
    dispatch(setSchedule(newSchedule))
  }
  const handleStartTimeChange = (
    dayIndex: number,
    timeSlotIndex: number,
    newStartTime: string
  ) => {
    const newSchedule = settings.horaire_hebdomadaire.map((day, dIndex) =>
      dIndex === dayIndex
        ? {
          ...day,
          creneau_horaire: day.creneau_horaire.map((slot, sIndex) =>
            sIndex === timeSlotIndex ? { ...slot, heure_debut: newStartTime } : slot
          ),
        }
        : day
    )

    dispatch(setSchedule(newSchedule))
  }

  const handleEndTimeChange = (
    dayIndex: number,
    timeSlotIndex: number,
    newEndTime: string
  ) => {
    const newSchedule = settings.horaire_hebdomadaire.map((day, dIndex) =>
      dIndex === dayIndex
        ? {
          ...day,
          creneau_horaire: day.creneau_horaire.map((slot, sIndex) =>
            sIndex === timeSlotIndex ? { ...slot, heure_fin: newEndTime } : slot
          ),
        }
        : day
    )

    dispatch(setSchedule(newSchedule))
  }

  const handleAvailabilityChange = (value: string) => {
    dispatch(setAvailabilityType(value));
    if (value === 'specifique') {
      const today = new Date();
      today.setHours(9, 0, 0, 0);
      dispatch(setDateSchedule([
        {
          date: today.toISOString(),
          est_specifique: true,
          creneau_horaire: [{ heure_debut: '09:00', heure_fin: '17:00' }]
        }
      ]));
      dispatch(setDateException([]))
    } else {
      dispatch(setDateSchedule([]));
    }
  };
  const handleAddDate = (date: Date | null) => {
    const updateDate = (prevDate: horaire_date_specifique[], est_specifique: boolean) => {
      const newDate: horaire_date_specifique = {
        date: date ? date.toISOString() : null,
        est_specifique: est_specifique,
        creneau_horaire: [{ heure_debut: '09:00', heure_fin: '17:00' }]
      }
      if (!prevDate) return [newDate];
      else return [...prevDate, newDate]
    }
    if (settings.type === "specifique") {
      const newDateSchedule = updateDate(settings.horaire_date_specifique, true)
      dispatch(setDateSchedule(newDateSchedule));
    } else {
      const newDateException = updateDate(settings.horaire_date_specifique, false)
      dispatch(setDateException(newDateException));
    }
  }
  const handleAddDateException = (date: Date | null, creneau_horaire: creneau_horaire) => {
    // si la date existe deja : 
    // - recuperer l'index de la date (dateIndex)
    // - verifier si le time slot existe (creneau_horaire) puis recuperer l'index du time slot (timeSlotIndex)
    //  -si le time slot n'existe pas : supprimer le time slot
    // si la date n'existe pas encore : 
    // - ajouter la date avec le time slot : (dateIndex=0, timeSlotIndex=0)
    // retourner le nouvel array [dateIndex, timeSlotIndex]

    const updateDate = (prevDate: horaire_date_specifique[]) => {
      if (prevDate.length === 0) {
        return [
          {
            date: date?.toISOString() || '',
            est_specifique: false,
            creneau_horaire: []
          }
        ];
      }
      const newDate = [...prevDate];
      const dateIdx = newDate.findIndex((d) => {
        return new Date(d.date).toISOString().split('T')[0] === date?.toISOString().split('T')[0]
      })

      if (dateIdx !== -1) {
        // Date exists, veriffier si le time slot existe
        const timeSlotIdx = newDate[dateIdx].creneau_horaire.findIndex(
          (slot) => slot.heure_debut === creneau_horaire.heure_debut &&
            slot.heure_fin === creneau_horaire.heure_fin
        );

        if (timeSlotIdx === -1) {
          // Time slot n'exist pas, ajouter la
          newDate[dateIdx] = {
            ...newDate[dateIdx],
            creneau_horaire: [...newDate[dateIdx].creneau_horaire, creneau_horaire]
          };
        } else {
          // Time slot exists, supprimer le time slot
          newDate[dateIdx] = {
            ...newDate[dateIdx],
            creneau_horaire: newDate[dateIdx].creneau_horaire.filter(
              (_, index) => index !== timeSlotIdx
            )
          };
        }
      } else {
        // Date n'exist pas, ajouter une nouvelle date avec le time slot
        const newDateItem = {
          date: date?.toISOString() || '',
          est_specifique: false,
          creneau_horaire: []
        };
        newDate.unshift(newDateItem); // Ajouter au debut (dateIndex=0)
      }
      return newDate;
    };

    const newDateException = updateDate(settings.horaire_date_specifique);
    dispatch(setDateException(newDateException));
    return newDateException;
  }
  const handleAddDateTimeSlot = (dateIndex: number) => {
    const updateDate = (prevDate: horaire_date_specifique[]) => {
      const newDate = [...prevDate]
      const newTimeSlot = calculateNextTimeSlot(newDate[dateIndex].creneau_horaire)

      newDate[dateIndex] = {
        ...newDate[dateIndex],
        creneau_horaire: [...newDate[dateIndex].creneau_horaire, newTimeSlot]
      }
      return newDate
    };
    checkSpecifique(updateDate)
  }
  const handleDeleteDateTimeSlot = (dateIndex: number, timeSlotIndex: number) => {
    const updateDate = (prevDate: horaire_date_specifique[]) => {
      const newDate = [...prevDate]
      newDate[dateIndex] = {
        ...newDate[dateIndex],
        creneau_horaire: newDate[dateIndex].creneau_horaire.filter(
          (_, index) => index !== timeSlotIndex
        )
      }
      // If creneau_horaire is empty, remove the entire date entry
      if (newDate[dateIndex].creneau_horaire.length === 0) {
        return newDate.filter((_, index) => index !== dateIndex);
      }
      return newDate
    };
    checkSpecifique(updateDate)
  }
  const handleDateStartTimeChange = (
    dateIndex: number,
    timeSlotIndex: number,
    newStartTime: string
  ) => {
    const updateDate = (prevDate: horaire_date_specifique[]) => {
      let newDate = [...prevDate]
      newDate = newDate.map((date, dIndex) =>
        dIndex === dateIndex
          ? {
            ...date,
            creneau_horaire: date.creneau_horaire.map((slot, sIndex) =>
              sIndex === timeSlotIndex ? { ...slot, heure_debut: newStartTime } : slot
            ),
          }
          : date
      )
      return newDate
    };
    checkSpecifique(updateDate)
  }

  const handleDateEndTimeChange = (
    dateIndex: number,
    timeSlotIndex: number,
    newEndTime: string
  ) => {
    const updateDate = (prevDate: horaire_date_specifique[]) => {
      let newDate = [...prevDate]
      newDate = newDate.map((date, dIndex) =>
        dIndex === dateIndex
          ? {
            ...date,
            creneau_horaire: date.creneau_horaire.map((slot, sIndex) =>
              sIndex === timeSlotIndex ? { ...slot, heure_fin: newEndTime } : slot
            ),
          }
          : date
      )
      return newDate
    };
    checkSpecifique(updateDate)
  }

  const handleDureePauseChange = (value: string) => {
    const newValue = parseInt(value);
    validateField('dureePause', newValue, errors, (newErrors) => {
      dispatch(setErrors(newErrors));
    });
    dispatch(setDureePause(newValue));
  }
  const handleMaxReservationsChange = (value: string) => {
    const newValue = parseInt(value);
    validateField('maxReservationsPerDay', newValue, errors, (newErrors) => {
      dispatch(setErrors(newErrors));
    });
    dispatch(setMaxReservationsPerDay(newValue))
  }
  const handleCanInviteOthersChange = (value: boolean) => {
    dispatch(setCanInviteOthers(value))
  }
  const handleStartDateChange = (date: Date | null) => {
    const isoString = date ? date.toISOString() : null;
    dispatch(setStartDate(isoString));
  }
  const handleEndDateChange = (date: Date | null) => {
    const isoString = date ? date.toISOString() : null;
    dispatch(setEndDate(isoString));
  }

  const handlePlanningTypeChange = (value: string) => {
    dispatch(setPlanningType(value));
  };
  const handleDurationChange = (value: string) => {
    const newValue = parseInt(value);
    validateField('duration', newValue, errors, (newErrors) => {
      dispatch(setErrors(newErrors));
    });
    dispatch(setDuration(newValue));
  };
  const handleIsMaxDays = (value: boolean) => {
    dispatch(setIsMaxDays(value))
  }
  const handleIsMinHours = (value: boolean) => {
    dispatch(setIsMinHours(value))
  }
  const handleIsBreak = (value: boolean) => {
    dispatch(setIsBreak(value))
  }
  const handleMaxDaysChange = (value: string) => {
    const newValue = parseInt(value);
    validateField('maxDays', newValue, errors, (newErrors) => {
      dispatch(setErrors(newErrors));
    });
    dispatch(setMaxDays(newValue))
  }
  const handleMinHoursChange = (value: string) => {
    const newValue = parseInt(value);
    validateField('minHours', newValue, errors, (newErrors) => {
      dispatch(setErrors(newErrors));
    });
    dispatch(setMinHours(newValue))
  }
  const handleIsMaxReservationsPerDay = (value: boolean) => {
    dispatch(setIsMaxReservationsPerDay(value))
  }
  const handleBreakDurationChange = (value: string) => {
    validateField('breakDuration', value, errors, (newErrors) => {
      dispatch(setErrors(newErrors));
    });
    dispatch(setBreakDuration(value))
  }

  const initializeSettingsState = (
    initialSettings: AvailabilitySettingsDTO,
    initialSettingsLocal: controle_parametre
  ) => {
    dispatch(initializeSettings({ initialSettings, initialSettingsLocal }))
  };
  const saveToLocalStorage = (newSettingsLocal: controle_parametre) => {
    localStorage.setItem('settingLocal', JSON.stringify(newSettingsLocal));
    dispatch(setSettingsLocal(newSettingsLocal));
  };

  const updateAllSettings = (newSettings: AvailabilitySettingsDTO) => {
    dispatch(updateSettings(newSettings));
  };

  const handleDeleteTimeSlotAt = (jour: string) => {
    const dayIndex = ["dimanche", "lundi", "mardi", "mercredi", "jeudi", "vendredi", "samedi"].indexOf(jour)
    const updateSchedule = (prevSchedule: horaire_hebdomadaire[]) => {
      const newSchedule = [...prevSchedule]
      const daySchedule = { ...newSchedule[dayIndex] }
      daySchedule.creneau_horaire = []
      newSchedule[dayIndex] = daySchedule
      return newSchedule
    };
    const newSchedule = updateSchedule(settings.horaire_hebdomadaire)
    dispatch(setSchedule(newSchedule))
    return newSchedule
  };

  const handleSettingSave = (professionalId: number) => {
    const dateSpecifique = settings.horaire_date_specifique;
    const AvailabilitySettings: AvailabilitySettingsDTO = {
      id_professionnel: professionalId,
      type: settings.type as "hebdomadaire" | "specifique",
      horaire_date_specifique: dateSpecifique,
      horaire_hebdomadaire:
        settings.type !== "specifique"
          ? settings.horaire_hebdomadaire
          : [
            { jour: "Dim.", creneau_horaire: [] },
            { jour: "Lun.", creneau_horaire: [] },
            { jour: "Mar.", creneau_horaire: [] },
            { jour: "Mer.", creneau_horaire: [] },
            { jour: "Jeu.", creneau_horaire: [] },
            { jour: "Ven.", creneau_horaire: [] },
            { jour: "Sam.", creneau_horaire: [] },
          ],
      date_debut: settings.date_debut ? new Date(settings.date_debut) : new Date(),
      date_fin: settings.date_fin ? new Date(settings.date_fin) : null,
      temps_moyen_consulation: settings.temps_moyen_consulation,
      duree_pause: settings.duree_pause,
      max_rdv_par_jours: settings.max_rdv_par_jours,
      peut_inviter_autre: settings.peut_inviter_autre,
      pauses: [],
    };
    return AvailabilitySettings;
  };

  return {
    settings,
    settingsLocal,
    errors,
    handleAddTimeSlot,
    handleDeleteTimeSlot,
    handleCopyTimeSlot,
    handleStartTimeChange,
    handleEndTimeChange,
    handleAvailabilityChange,
    handleAddDate,
    handleAddDateTimeSlot,
    handleDeleteDateTimeSlot,
    handleDateStartTimeChange,
    handleDateEndTimeChange,
    handleDureePauseChange,
    handleMaxReservationsChange,
    handleCanInviteOthersChange,
    handleStartDateChange,
    handleEndDateChange,
    handlePlanningTypeChange,
    handleDurationChange,
    handleIsMaxDays,
    handleIsMinHours,
    handleIsBreak,
    handleMaxDaysChange,
    handleMinHoursChange,
    handleIsMaxReservationsPerDay,
    handleBreakDurationChange,
    handleDeleteTimeSlotAt,
    handleAddDateException,
    initializeSettingsState,
    saveToLocalStorage,
    updateAllSettings,
    handleSettingSave,
  };
};
