// src/presentation/hooks/use-dashboard-data.ts

import { useEffect, useMemo, useState } from "react";
import {
  Revenue,
  PatientMetricsData,
} from "@/presentation/types/professional.types";
import { useConsultationState } from "./use-consultation-state";
import { useProfessionnelPatient } from "./use-professionnelPatient";
import { useFacturation } from "./use-facturation";
import { rendez_vous_statut_enum } from "@/domain/models/enums";

export const useDashboardData = (professionalId: number) => {
  const { appointmentProfessional, fetchAppointmentListByProfessionalId } =
    useConsultationState();
  const { dataProfessionalPatients, getProfessionnelPatient } =
    useProfessionnelPatient();
  const { listeFacturationProfessional, getFacturationsByProfessionalId } =
    useFacturation();

  // Données de métriques patients
  const [patientMetrics, setPatientMetrics] =
    useState<PatientMetricsData>(null);

  // Données de revenus
  const [revenue, setRevenue] = useState<Revenue>({
    monthly: 0,
    trend: { percentage: 0, isPositive: true },
  });

  // Données pour le graphique de revenus
  const [revenueData, setRevenueData] = useState([]);

  // Données pour le graphique de distribution des patients
  const [patientDistribution, setPatientDistribution] = useState([
    { name: "Nouveaux", value: 0, color: "#4F46E5" },
    { name: "Réguliers", value: 0, color: "#10B981" },
    { name: "Occasionnels", value: 0, color: "#F59E0B" },
  ]);

  // Statistiques des rendez-vous
  const [appointmentStats, setAppointmentStats] = useState({
    completed: 0,
    upcoming: 0,
    cancelled: 0,
    totalThisMonth: 0,
    completedThisMonth: 0,
  });

  // Charger les données initiales
  useEffect(() => {
    if (professionalId) {
      getProfessionnelPatient(professionalId);
      getFacturationsByProfessionalId(professionalId);
      fetchAppointmentListByProfessionalId(professionalId);
    }
  }, [professionalId]);

  // Mise à jour des métriques patients
  useEffect(() => {
    if (dataProfessionalPatients) {
      const totalPatients = dataProfessionalPatients.length;

      // Calculer les nouveaux patients de ce mois-ci
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const newPatients = dataProfessionalPatients.filter((patient) => {
        try {
          const createdDate = new Date(patient.created_date);
          // Vérifier si la date de création est dans le mois en cours
          return createdDate >= firstDayOfMonth && createdDate <= now;
        } catch (error) {
          return false;
        }
      }).length;

      // Calculer les patients réguliers (plus d'une visite)
      const patientVisitCounts: Record<string, number> = {};
      appointmentProfessional?.forEach((appointment) => {
        const patientId = appointment.patient?.id;
        if (patientId) {
          patientVisitCounts[patientId] =
            (patientVisitCounts[patientId] || 0) + 1;
        }
      });

      const returningPatients = Object.values(patientVisitCounts).filter(
        (count) => count > 1
      ).length;

      // Mise à jour des métriques
      setPatientMetrics({
        totalPatients,
        newPatients,
        returningPatients,
        averageVisitTime: "45 min", // À calculer si les données sont disponibles
        trends: {
          newPatients: Math.round((newPatients / totalPatients) * 100) || 0,
          returningPatients:
            Math.round((returningPatients / totalPatients) * 100) || 0,
        },
      });

      // Mise à jour de la distribution des patients
      setPatientDistribution([
        { name: "Nouveaux", value: newPatients, color: "#4F46E5" },
        { name: "Réguliers", value: returningPatients, color: "#10B981" },
        {
          name: "Occasionnels",
          value: totalPatients - newPatients - returningPatients,
          color: "#F59E0B",
        },
      ]);
    }
  }, [dataProfessionalPatients, appointmentProfessional]);

  // Mise à jour des statistiques de rendez-vous
  useEffect(() => {
    if (appointmentProfessional) {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Filtrer les rendez-vous du mois en cours
      const thisMonthAppointments = appointmentProfessional.filter(
        (appointment) => {
          try {
            const appointmentDate = new Date(appointment.date_rendez_vous);
            return appointmentDate >= firstDayOfMonth && appointmentDate <= now;
          } catch (error) {
            return false;
          }
        }
      );

      // Compter les rendez-vous par statut (tous)
      const completed = appointmentProfessional.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.TERMINER
      ).length;

      const upcoming = appointmentProfessional.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.A_VENIR
      ).length;

      const cancelled = appointmentProfessional.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.ANNULER
      ).length;

      // Compter les rendez-vous terminés du mois en cours
      const completedThisMonth = thisMonthAppointments.filter(
        (appointment) => appointment.statut === rendez_vous_statut_enum.TERMINER
      ).length;

      setAppointmentStats({
        completed,
        upcoming,
        cancelled,
        totalThisMonth: thisMonthAppointments.length,
        completedThisMonth: completedThisMonth,
      });
    }
  }, [appointmentProfessional]);

  // Mise à jour des données de revenus
  useEffect(() => {
    if (listeFacturationProfessional) {
      // Calculer le revenu total du mois en cours
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const thisMonthFacturations = listeFacturationProfessional.filter(
        (facture) => {
          try {
            const factureDate = new Date(facture.date_creation);
            return factureDate >= firstDayOfMonth && factureDate <= now;
          } catch (error) {
            return false;
          }
        }
      );

      const lastMonthFirstDay = new Date(
        now.getFullYear(),
        now.getMonth() - 1,
        1
      );
      const lastMonthLastDay = new Date(now.getFullYear(), now.getMonth(), 0);

      const lastMonthFacturations = listeFacturationProfessional.filter(
        (facture) => {
          try {
            const factureDate = new Date(facture.date_creation);
            return (
              factureDate >= lastMonthFirstDay &&
              factureDate <= lastMonthLastDay
            );
          } catch (error) {
            return false;
          }
        }
      );

      // Calculer le montant total des facturations du mois en cours
      const thisMonthTotal = thisMonthFacturations.reduce(
        (total, facture) => total + (facture.montant || 0),
        0
      );

      // Calculer le montant total des facturations du mois précédent
      const lastMonthTotal = lastMonthFacturations.reduce(
        (total, facture) => total + (facture.montant || 0),
        0
      );

      // Calculer la tendance
      let trendPercentage = 0;
      let isPositive = true;

      if (lastMonthTotal > 0) {
        trendPercentage = Math.round(
          ((thisMonthTotal - lastMonthTotal) / lastMonthTotal) * 100
        );
        isPositive = trendPercentage >= 0;
        trendPercentage = Math.abs(trendPercentage);
      }

      setRevenue({
        monthly: thisMonthTotal,
        trend: { percentage: trendPercentage, isPositive },
      });

      // Générer les données pour le graphique de revenus (6 derniers mois)
      const last6Months = [];
      const monthNames = [
        "Jan",
        "Fév",
        "Mar",
        "Avr",
        "Mai",
        "Juin",
        "Juil",
        "Août",
        "Sep",
        "Oct",
        "Nov",
        "Déc",
      ];

      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = monthNames[monthDate.getMonth()];
        const monthFirstDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1
        );
        const monthLastDay = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0
        );

        const monthFacturations = listeFacturationProfessional.filter(
          (facture) => {
            try {
              const factureDate = new Date(facture.date_creation);
              return (
                factureDate >= monthFirstDay && factureDate <= monthLastDay
              );
            } catch (error) {
              return false;
            }
          }
        );

        const monthTotal = monthFacturations.reduce(
          (total, facture) => total + (facture.montant || 0),
          0
        );

        last6Months.push({
          name: monthName,
          revenue: monthTotal,
        });
      }

      setRevenueData(last6Months);
    }
  }, [listeFacturationProfessional]);

  // Filtrer les rendez-vous du jour
  const todayAppointments = useMemo(() => {
    // Si pas de rendez-vous, retourner un tableau vide
    if (!appointmentProfessional || appointmentProfessional.length === 0) {
      return [];
    }

    // Rendez-vous du jour
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0];

    return appointmentProfessional.filter((appointment) => {
      try {
        // Vérifier si date_rendez_vous existe
        if (!appointment.date_rendez_vous) {
          return false;
        }

        // Essayer de créer un objet Date
        const appointmentDate = new Date(appointment.date_rendez_vous);

        // Vérifier si la date est valide
        if (isNaN(appointmentDate.getTime())) {
          return false;
        }

        const appointmentDateStr = appointmentDate.toISOString().split("T")[0];
        return appointmentDateStr === todayStr;
      } catch (error) {
        return false;
      }
    });
  }, [appointmentProfessional]);

  // Calculer le taux de complétion des rendez-vous (uniquement pour le mois en cours)
  const completionRate =
    appointmentStats.totalThisMonth > 0
      ? Math.round(
          (appointmentStats.completedThisMonth /
            appointmentStats.totalThisMonth) *
            100
        )
      : 0;

  return {
    patientMetrics,
    revenue,
    revenueData,
    patientDistribution,
    appointmentStats,
    todayAppointments,
    completionRate,
    loading: {
      appointments: appointmentProfessional === null,
      patients: dataProfessionalPatients === null,
      facturations: listeFacturationProfessional === null,
    },
  };
};
