import { useCallback } from 'react'
import { useAppDispatch, useAppSelector } from './redux'
import {
    createEvenement,
    deleteEvenement,
    deleteEvenementByProfessionalId,
    getEvenement,
    updateEvenement,
    updateEvenementByProfessionalId,
    setIsPopOverOpen,
    reset
} from '@/application/slices/professionnal/evenementSlice'
import { Evenement } from '@/domain/models'

export const useEvenement = () => {
    const dispatch = useAppDispatch()
    const { evenement, currentEvenement, loading, error, isPopoverOpen } =
        useAppSelector((state) => state.evenement)

    const fetchEvenements = useCallback(
        async (professionalId: number) => {
            if (!navigator.onLine) {
                throw new Error(
                    'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
                )
            }
            await dispatch(getEvenement(professionalId)).unwrap()
        },
        [dispatch]
    )

    const createNewEvenement = useCallback(
        async (evenementData: Omit<Evenement, 'id'>) => {
            const result = await dispatch(createEvenement(evenementData)).unwrap()
            return result
        },
        [dispatch]
    )

    const updateExistingEvenement = useCallback(
        async (id: number, evenementData: Partial<Evenement>) => {
            const result = await dispatch(
                updateEvenement({ id, evenement: evenementData })
            ).unwrap()
            return result
        },
        [dispatch]
    )

    const updateEvenementByProfessional = useCallback(
        async (id: number, evenementData: Omit<Evenement, 'id'>) => {
            const result = await dispatch(
                updateEvenementByProfessionalId({ id, evenement: evenementData })
            ).unwrap()
            return result
        },
        [dispatch]
    )

    const deleteExistingEvenement = useCallback(
        async (id: number) => {
            await dispatch(deleteEvenement(id)).unwrap()
        },
        [dispatch]
    )

    const deleteEvenementByProfessional = useCallback(
        async (id: number) => {
            await dispatch(deleteEvenementByProfessionalId(id)).unwrap()
        },
        [dispatch]
    )

    const togglePopover = useCallback(
        (isOpen: boolean) => {
            dispatch(setIsPopOverOpen(isOpen))
        },
        [dispatch]
    )

    const resetCurrentEvenement = useCallback(
        () => {
            dispatch(reset())
        },
        [dispatch]
    )

    return {
        evenements: evenement,
        loading,
        error,
        isPopoverOpen,
        fetchEvenements,
        currentEvenement,
        createNewEvenement,
        updateExistingEvenement,
        updateEvenementByProfessional,
        deleteExistingEvenement,
        deleteEvenementByProfessional,
        togglePopover,
        resetCurrentEvenement,
    }
}
