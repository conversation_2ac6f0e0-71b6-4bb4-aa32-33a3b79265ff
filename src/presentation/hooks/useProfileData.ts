import { useCallback, useEffect, useMemo, useState } from "react";
import { ProfessionalCardDTO } from "@/domain/DTOS";
import useSearchProfessional from "./use-search-professional";
import { toast } from "sonner";

/**
 * Interface pour les données étendues du profil professionnel
 * Inclut les nouvelles informations du formulaire d'inscription
 */
export interface ExtendedProfessionalProfile extends ProfessionalCardDTO {
  // Informations professionnelles étendues
  diplomes?: Array<{
    id?: number;
    nom_diplome: string;
    etablissement: string;
    date_obtention: string;
    description?: string;
  }>;
  experiences?: Array<{
    id?: number;
    poste: string;
    etablissement: string;
    date_debut: string;
    date_fin?: string;
    est_actuel: boolean;
    description?: string;
  }>;
  publications?: Array<{
    id?: number;
    titre: string;
    auteurs: string;
    date_publication: string;
    lien?: string;
    description?: string;
  }>;
  langues?: Array<{
    id?: number;
    nom_langue: string;
    niveau: string;
  }>;
  motCles?: Array<{
    id?: number;
    mot_cle: string;
  }>;
  // Informations de contact publiques (filtrées)
  modes_paiement_acceptes?: string[];
  raison_sociale?: string;
  informations_acces?: string;
}

/**
 * Hook personnalisé pour la gestion des données du profil professionnel
 * Suit le pattern useDashboardData avec gestion d'erreurs et loading states
 *
 * @param professionalId - ID du professionnel à récupérer
 * @returns Données du profil, états de chargement et fonctions utilitaires
 */
export const useProfileData = (professionalId: number | null) => {
  const { currentProfessional, searchProfessionalById, loading } =
    useSearchProfessional();
  const [profileData, setProfileData] = useState<
    ExtendedProfessionalProfile | null
  >(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fonction pour filtrer les informations sensibles
   * Assure que seules les informations publiques sont affichées
   */
  const filterSensitiveData = useCallback(
    (professional: ProfessionalCardDTO): ExtendedProfessionalProfile => {
      // Créer une copie des données en excluant les informations sensibles
      const {
        // Exclure les informations sensibles
        // email, telephone sont déjà exclus du DTO public
        ...publicData
      } = professional;

      return {
        ...publicData,
        // Ajouter des champs par défaut pour les nouvelles fonctionnalités
        diplomes: [],
        experiences: [],
        publications: [],
        langues: [],
        motCles: [],
        modes_paiement_acceptes: [],
      } as ExtendedProfessionalProfile;
    },
    [],
  );

  /**
   * Récupération des données du professionnel
   * Removed searchProfessionalById from dependencies to prevent infinite re-renders
   */
  const fetchProfileData = useCallback(async (id: number) => {
    try {
      setError(null);
      await searchProfessionalById({ id });
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : "Erreur lors de la récupération du profil";
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, []); // Empty dependency array - searchProfessionalById should be stable

  /**
   * Effet pour charger les données initiales
   */
  useEffect(() => {
    if (professionalId) {
      fetchProfileData(professionalId);
    }
  }, [professionalId, fetchProfileData]);

  /**
   * Effet pour traiter les données récupérées
   * Removed filterSensitiveData from dependencies to prevent infinite re-renders
   */
  useEffect(() => {
    if (currentProfessional) {
      const filteredData = filterSensitiveData(currentProfessional);
      setProfileData(filteredData);
    }
  }, [currentProfessional]); // Only depend on currentProfessional

  /**
   * Memoized computed values to prevent unnecessary re-calculations
   */
  const acceptsNewPatients = useMemo(
    () => profileData?.nouveau_patient_acceptes ?? false,
    [profileData?.nouveau_patient_acceptes],
  );

  const primarySpecialty = useMemo(
    () =>
      profileData?.specialite?.[0]?.nom_specialite ||
      "Spécialité non renseignée",
    [profileData?.specialite],
  );

  const fullAddress = useMemo(() => {
    if (!profileData) return "";
    return [
      profileData.adresse,
      profileData.commune,
      profileData.district,
      profileData.region,
    ].filter(Boolean).join(", ");
  }, [
    profileData?.adresse,
    profileData?.commune,
    profileData?.district,
    profileData?.region,
  ]);

  const hasCredentials = useMemo(() =>
    Boolean(
      profileData?.diplomes?.length ||
        profileData?.experiences?.length ||
        profileData?.publications?.length,
    ), [
    profileData?.diplomes?.length,
    profileData?.experiences?.length,
    profileData?.publications?.length,
  ]);

  const hasLanguages = useMemo(() => Boolean(profileData?.langues?.length), [
    profileData?.langues?.length,
  ]);

  const hasKeywords = useMemo(() => Boolean(profileData?.motCles?.length), [
    profileData?.motCles?.length,
  ]);

  const refetchProfile = useCallback(() => {
    if (professionalId) {
      fetchProfileData(professionalId);
    }
  }, [professionalId, fetchProfileData]);

  return {
    // Données principales
    profileData,

    // États de chargement et d'erreur
    loading,
    error,

    // Fonctions utilitaires
    refetchProfile,
    acceptsNewPatients,
    primarySpecialty,
    fullAddress,

    // Données dérivées pour l'affichage
    hasCredentials,
    hasLanguages,
    hasKeywords,
  };
};
