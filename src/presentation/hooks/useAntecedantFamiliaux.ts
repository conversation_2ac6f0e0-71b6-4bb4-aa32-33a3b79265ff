import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { AntecedantFamilliaux } from '@/domain/models'
import {
  createAntecedantFamiliaux,
  getAntecedantFamiliaux,
  updateAntecedantFamiliaux,
  deleteAntecedantFamiliaux,
  setSelectedAntecedantFamiliaux,
  setAffection,
  setDecede,
  setRemarks,
  resetAntecedantFamiliauxState,
  clearSelectedAntecedantFamiliaux
} from '@/application/slices/professionnal/antecedantFamiliauxSlice'

export const useAntecedantFamiliaux = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    antecedantFamiliaux,
    selectedAntecedantFamiliaux,
    antecedantFamiliauxState,
    loading,
    error
  } = useSelector((state: RootState) => state.antecedantFamiliaux)

  const create = useCallback(
    async (data: Omit<AntecedantFamilliaux, "id">[]) => {
      const register = await dispatch(createAntecedantFamiliaux(data))
      const antecedants = register.payload as AntecedantFamilliaux[]
      return antecedants.map(antecedant => {
        return {
          id: antecedant.id,
          detail: antecedant.nom_lien
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getAntecedantFamiliaux(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<AntecedantFamilliaux>) => {
      await dispatch(updateAntecedantFamiliaux({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteAntecedantFamiliaux(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (antecedantFamiliaux: AntecedantFamilliaux | null) => {
      dispatch(setSelectedAntecedantFamiliaux(antecedantFamiliaux))
    },
    [dispatch]
  )

  const handleAffectionChange = (item: string, value: string) => {
    dispatch(setAffection({item, value}))
  };

  const handleDecedeChange = (item: string, value: boolean) => {
    dispatch(setDecede({item, value}))
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetAntecedantFamiliauxState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedAntecedantFamiliaux())
  }, [dispatch])

  return {
    antecedantFamiliaux,
    selectedAntecedantFamiliaux,
    antecedantFamiliauxState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleAffectionChange,
    handleDecedeChange,
    handleRemarksChange,
    resetState,
    clearSelected
  }
}
