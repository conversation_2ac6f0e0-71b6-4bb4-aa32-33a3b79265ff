import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { Vaccination } from '@/domain/models'
import {
  createVaccination,
  getVaccinations,
  updateVaccination,
  deleteVaccination,
  setSelectedVaccination,
  setDateAdministration,
  setProchaineDate,
  setRemarks,
  resetVaccinationState,
  clearSelectedVaccination
} from '@/application/slices/professionnal/vaccinationSlice'
import { getLocalISOString } from '@/shared/utils/getLocalISOString'

export const useVaccination = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    vaccinations,
    selectedVaccination,
    vaccinationState,
    loading,
    error
  } = useSelector((state: RootState) => state.vaccination)

  const create = useCallback(
    async (data: Omit<Vaccination, "id">[]) => {
      const register = await dispatch(createVaccination(data))
      const vaccinations = register.payload as Vaccination[]
      return vaccinations.map(vaccination => {
        return {
          id: vaccination.id,
          detail: vaccination.nom_vaccin
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getVaccinations(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<Vaccination>) => {
      await dispatch(updateVaccination({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteVaccination(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (vaccination: Vaccination | null) => {
      dispatch(setSelectedVaccination(vaccination))
    },
    [dispatch]
  )

  const handleDateAdministrationChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setDateAdministration({item, value: getLocalISOString(value)}));
  };
  
  const handleProchaineDateChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setProchaineDate({item, value: getLocalISOString(value)}));
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetVaccinationState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedVaccination())
  }, [dispatch])

  return {
    vaccinations,
    selectedVaccination,
    vaccinationState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleRemarksChange,
    handleDateAdministrationChange,
    handleProchaineDateChange,
    resetState,
    clearSelected
  }
}
