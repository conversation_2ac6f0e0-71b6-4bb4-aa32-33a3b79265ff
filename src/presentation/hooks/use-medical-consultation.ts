import { useCallback } from 'react'
import { useAppDispatch, useAppSelector } from './redux'
import {
    fetchMedicalConsultationById,
    fetchMedicalConsultationsByProfessionalId,
    fetchMedicalConsultationsByPatientId,
    createMedicalConsultation,
    updateMedicalConsultation,
    deleteMedicalConsultation,
    clearCurrentMedicalConsultation,
    clearError,
} from '@/application/slices/professionnal/medicalConsultationSlice'
import { consultation_medical } from '@/domain/models'

export const useMedicalConsultation = () => {
    const dispatch = useAppDispatch()
    const { consultations, consultationsProfessional, currentConsultation, loading, error } =
        useAppSelector((state) => state.medicalConsultation)

    const getMedicalConsultationById = useCallback(
        async (id: number) => {
            if (!navigator.onLine) {
                throw new Error(
                    'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
                )
            }
            await dispatch(fetchMedicalConsultationById(id)).unwrap()
        },
        [dispatch]
    )

    const getMedicalConsultationsByProfessionalId = useCallback(
        async (professionalId: number) => {
            if (!navigator.onLine) {
                throw new Error(
                    'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
                )
            }
            await dispatch(fetchMedicalConsultationsByProfessionalId(professionalId)).unwrap()
        },
        [dispatch]
    )

    const getMedicalConsultationsByPatientId = useCallback(
        async (patientId: number) => {
            if (!navigator.onLine) {
                throw new Error(
                    'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
                )
            }
            await dispatch(fetchMedicalConsultationsByPatientId(patientId)).unwrap()
        },
        [dispatch]
    )

    const handleCreateMedicalConsultation = useCallback(
        async (consultationData: Omit<consultation_medical, 'id'>) => {
            const result = await dispatch(createMedicalConsultation(consultationData)).unwrap()
            return result
        },
        [dispatch]
    )

    const handleUpdateMedicalConsultation = useCallback(
        async (id: number, consultationData: Partial<consultation_medical>) => {
            const result = await dispatch(
                updateMedicalConsultation({ id, consultation: consultationData })
            ).unwrap()
            return result
        },
        [dispatch]
    )

    const handleDeleteMedicalConsultation = useCallback(
        async (id: number) => {
            if (!navigator.onLine) {
                throw new Error(
                    'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
                )
            }
            const result = await dispatch(deleteMedicalConsultation(id)).unwrap()
            return result
        },
        [dispatch]
    )

    return {
        consultations,
        consultationsProfessional,
        currentConsultation,
        loading,
        error,
        getMedicalConsultationById,
        getMedicalConsultationsByProfessionalId,
        getMedicalConsultationsByPatientId,
        handleCreateMedicalConsultation,
        handleUpdateMedicalConsultation,
        handleDeleteMedicalConsultation,
    }
}
