import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import {
  resetAuth,
  resetState,
  setActiveStep,
  setIsPageValid,
  setProcheInfoField,
  updateFormData,
} from "@/application/slices/patient/consultationStateSlice";
import {
  categorie_enum,
  rendez_vous_statut_enum,
  sexe_enum,
} from "@/domain/models/enums";
import { Patient, Proche, RendezVous } from "@/domain/models";
import {
  cancelAppointment,
  cancelAppointmentByProfessional,
  completeAppointment,
  createAppointment,
  getAppointmentListByPatientId,
  getAppointmentListByProfessionalId,
  postponeAppointment,
} from "@/application/slices/professionnal/appointmentSlice";
import { createProche } from "@/application/slices/patient/prochePatientSlice";
import { useToast } from "./use-toast";
import { useEffect } from "react";
import { AnnulerRendezVous } from "@/domain/models/AnnulerRendezVous";
import { useLocation } from "react-router-dom";
import { CreateProfessionalPatientDTO } from "@/domain/DTOS";
import { AppointmentProfessionalDTO } from "@/domain/DTOS/AppointmentProfessionalDTO";
import { registerProfessionalPatient } from "@/application/slices/professionnal/professionnelPatientSlice";
import { registerUser } from "@/application/slices/auth/authSlice";
import { SignUpProps } from "@/domain/interfaces/repositories";
import { SuccessMessages } from "@/shared/constants/SuccessMessages";
import { ErrorMessages } from "@/shared/constants/ErrorMessages";

export const useConsultationState = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const id = searchParams.get("id");
  const date = searchParams.get("date");
  const start = searchParams.get("start");
  const end = searchParams.get("end");

  const dispatch = useDispatch<AppDispatch>();
  const {
    activeStep,
    isPageValid,
    formData: {
      dateRDV,
      timeRDV,
      categorie,
      speciality,
      consultationType,
      consultationReason,
      consultationMotif,
      phone,
      email,
      confirmEmail,
      password,
      acceptConditions,
      forWhom,
      procheInfo,
    },
  } = useSelector((state: RootState) => state.consultationState);
  const {
    loading,
    appointments,
    timeSlot,
    appointmentPatient,
    appointmentProfessional,
    error,
    professional,
  } = useSelector((state: RootState) => state.appointment);

  const { userData } = useSelector(
    (state: RootState) => state.authentification,
  );
  const toast = useToast();

  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [toast, error]);

  const handleCreateAppointment = async (stepIndex: number) => {
    dispatch(setActiveStep(stepIndex));

    const appointmentDate = new Date(date);
    const [hour, minute] = start.split(":");

    appointmentDate.setHours(
      Number.parseInt(hour),
      Number.parseInt(minute),
      0,
      0,
    );

    const data: Omit<RendezVous, "id"> = {
      categorie: categorie as categorie_enum,
      date_rendez_vous: appointmentDate.toISOString(),
      est_absent: false,
      id_professionnel: Number(id),
      patient_id: userData.id,
      raison: consultationReason,
      motif: consultationMotif,
      rappel_envoye: false,
      statut: rendez_vous_statut_enum.A_VENIR,
    };
    const professionnelPatientData = {
      ...userData as Patient,
      id_professionnel: Number(id),
      type: "create" as const,
    };
    await dispatch(createAppointment(data));
    if (forWhom == "other") await dispatch(createProche(procheInfo));
    await dispatch(
      registerProfessionalPatient(professionnelPatientData),
    )
      .unwrap();
  };

  const registerPatient = async (credentials: SignUpProps) => {
    const result = await dispatch(registerUser(credentials));

    if (result.meta.requestStatus === "fulfilled") {
      toast.success(SuccessMessages.REGISTER_SUCCESS);
    } else if (result.meta.requestStatus === "rejected") {
      const error = result.payload as { message: string };
      toast.error(error?.message || ErrorMessages.UNKNOWN_ERROR);
    }
  };

  const handleCreateAppointmentByProfessional = async (
    data: Omit<RendezVous, "id">,
  ) => {
    await dispatch(createAppointment(data)).unwrap();
  };

  const handleDateRDVChange = (value: Date) => {
    const isoString = value ? value.toISOString() : null;
    dispatch(updateFormData({ dateRDV: isoString }));
  };

  const handleTimeRDVChange = (value: string) => {
    dispatch(updateFormData({ timeRDV: value }));
  };

  const handleCategorieChange = (value: string) => {
    dispatch(updateFormData({ categorie: value }));
  };

  const handleIsPageValidChange = (value: boolean) => {
    dispatch(setIsPageValid(value));
  };

  const handleSpecialityChange = (value: string) => {
    dispatch(updateFormData({ speciality: value }));
  };

  const handleConsultationTypeChange = (value: string) => {
    dispatch(updateFormData({ consultationType: value }));
  };

  const handleConsultationReasonChange = (value: string) => {
    dispatch(updateFormData({ consultationReason: value }));
  };

  const handleConsultationMotifChange = (value: string) => {
    dispatch(updateFormData({ consultationMotif: value }));
  };

  const handlePhoneChange = (value: string) => {
    dispatch(updateFormData({ phone: value }));
  };

  const handleEmailChange = (value: string) => {
    dispatch(updateFormData({ email: value }));
  };

  const handleConfirmEmailChange = (value: string) => {
    dispatch(updateFormData({ confirmEmail: value }));
  };

  const handlePasswordChange = (value: string) => {
    dispatch(updateFormData({ password: value }));
  };

  const handleAcceptConditionsChange = (value: boolean) => {
    dispatch(updateFormData({ acceptConditions: value }));
  };

  const handleForWhomChange = (value: string) => {
    if (value === "self") {
      handleProcheInfoChange(null);
    }
    dispatch(updateFormData({ forWhom: value }));
  };

  const handleProcheInfoChange = (value: Omit<Proche, "id">) => {
    dispatch(updateFormData({ procheInfo: value }));
  };

  const handleProcheInfoBirthDateChange = (value: string) => {
    dispatch(setProcheInfoField({ date_naissance: value }));
  };

  const handleProcheInfoFirstNameChange = (value: string) => {
    dispatch(setProcheInfoField({ prenom: value }));
  };

  const handleProcheInfoLastNameChange = (value: string) => {
    dispatch(setProcheInfoField({ nom: value }));
  };

  const handleProcheInfoSexChange = (value: sexe_enum) => {
    dispatch(setProcheInfoField({ sexe: value }));
  };

  const handleActiveStepChange = (value: number) => {
    dispatch(setActiveStep(value));
  };

  const handleresetAuth = () => {
    dispatch(resetAuth());
  };

  const handleresetState = () => {
    dispatch(resetState());
  };

  const fetchAppointmentListByProfessionalId = async (id: number) => {
    await dispatch(getAppointmentListByProfessionalId(id));
  };

  const fetchAppointmentListByPatientId = async (id: number) => {
    await dispatch(getAppointmentListByPatientId(id));
  };

  const handleCancelAppointment = async (
    appointment: AppointmentProfessionalDTO,
  ) => {
    await dispatch(cancelAppointment(appointment));
  };

  const handleCancelAppointmentByProfessional = (
    appointment: Omit<AnnulerRendezVous, "id">,
  ) => {
    cancelAppointmentByProfessional(appointment);
  };

  const handleCompleteAppointment = async (id: number) => {
    await dispatch(completeAppointment(id));
  };

  const handlePostponeAppointment = async (
    id: number,
    dateRendezVous: Date,
  ) => {
    await dispatch(postponeAppointment({ id, dateRendezVous }));
  };

  return {
    dateRDV,
    timeRDV,
    activeStep,
    isPageValid,
    speciality,
    consultationType,
    consultationReason,
    consultationMotif,
    phone,
    email,
    confirmEmail,
    password,
    acceptConditions,
    forWhom,
    procheInfo,
    selectedProfessional: professional,
    selectedTimeSlot: timeSlot,
    userInformations: userData as Patient,
    categorie,
    appointments,
    appointmentPatient,
    appointmentProfessional,
    loading,
    registerPatient,
    handleCategorieChange,
    handleCreateAppointment,
    handleIsPageValidChange,
    handleSpecialityChange,
    handleConsultationTypeChange,
    handleConsultationReasonChange,
    handleConsultationMotifChange,
    handlePhoneChange,
    handleEmailChange,
    handleConfirmEmailChange,
    handlePasswordChange,
    handleAcceptConditionsChange,
    handleForWhomChange,
    handleProcheInfoChange,
    handleProcheInfoSexChange,
    handleProcheInfoBirthDateChange,
    handleProcheInfoFirstNameChange,
    handleProcheInfoLastNameChange,
    handleresetAuth,
    handleActiveStepChange,
    handleCancelAppointment,
    fetchAppointmentListByProfessionalId,
    fetchAppointmentListByPatientId,
    handleCancelAppointmentByProfessional,
    handleCompleteAppointment,
    handlePostponeAppointment,
    handleresetState,
    handleCreateAppointmentByProfessional,
    handleDateRDVChange,
    handleTimeRDVChange,
  };
};
