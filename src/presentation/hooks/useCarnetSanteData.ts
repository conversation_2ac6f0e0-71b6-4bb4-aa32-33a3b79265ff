import { useEffect, useState } from "react";
import { useCarnetSante } from "./useCarnetSante";
import { useAllergie } from "./useAllergie";
import { useMedicament } from "./useMedicament";
import { useAffectationMedicale } from "./useAffectationMedicale";
import { useDispositifMedicaux } from "./useDispositifMedicaux";
import { useAntecedantChirurgicaux } from "./useAntecedantChirurgicaux";
import { useAntecedantFamiliaux } from "./useAntecedantFamiliaux";
import { useAntecedentSociaux } from "./useAntecedentSociaux";
import { useVaccination } from "./useVaccination";
import { CarnetSante } from "@/domain/models";
import { CarnetSanteDTO } from "@/domain/DTOS";
import { useConditionGynecologique } from "./useConditionGynecologique";
import { useAntecedentGrossesse } from "./useAntecedentGrossesse";
import { useSigneVitaux } from "./useSigneVitaux";
import { useMedicalConsultation } from "./use-medical-consultation";
import { useDiagnostic } from "./useDiagnostic";
import { useFacturation } from "./use-facturation";

// hooks/useCarnetSanteData.ts
export const useCarnetSanteData = (patientId?: number) => {
  const {
    idCarnetSante,
    loading,
    getId: getIdCarnetSantes,
    create: createCarnetSante,
  } = useCarnetSante();
  const { allergies, getAll: getAllergies } = useAllergie();
  const { medicaments, getAll: getMedicaments } = useMedicament();
  const { affectationMedicales, getAll: getAllAffectationMedicales } =
    useAffectationMedicale();
  const { dispositifMedicaux, getAll: getAllDispositifMedicaux } =
    useDispositifMedicaux();
  const { antecedantChirurgicaux, getAll: getAllAntecedantChirurgicaux } =
    useAntecedantChirurgicaux();
  const { antecedantFamiliaux, getAll: getAllAntecedantFamiliaux } =
    useAntecedantFamiliaux();
  const { antecedentSociaux, getAll: getAllAntecedentSociaux } =
    useAntecedentSociaux();
  const { vaccinations, getAll: getAllVaccinations } = useVaccination();
  const { conditionGynecologique, getAll: getAllConditionGynecologique } =
    useConditionGynecologique();
  const { antecedentGrossesse, getAll: getAllAntecedentGrossesse } =
    useAntecedentGrossesse();
  const { signeVitaux, getAll: fetchSigneVitaux } = useSigneVitaux();
  const { diagnostics, getAll: getAllDiagnostics } = useDiagnostic();
  const { consultations, getMedicalConsultationsByPatientId } =
    useMedicalConsultation();
  const { listeFacturationPatient, getFacturationsByPatientId } =
    useFacturation();

  const [isSubmite, seIsSubmite] = useState(false);

  useEffect(() => {
    if (patientId) {
      getIdCarnetSantes(patientId);
      getFacturationsByPatientId(patientId);
    }
  }, [patientId, isSubmite]);

  useEffect(() => {
    if (idCarnetSante) {
      getAllergies(idCarnetSante);
      getMedicaments(idCarnetSante);
      getAllAffectationMedicales(idCarnetSante);
      getAllDispositifMedicaux(idCarnetSante);
      getAllAntecedantChirurgicaux(idCarnetSante);
      getAllAntecedantFamiliaux(idCarnetSante);
      getAllAntecedentSociaux(idCarnetSante);
      getAllVaccinations(idCarnetSante);
      getAllConditionGynecologique(idCarnetSante);
      getAllAntecedentGrossesse(idCarnetSante);
      getAllDiagnostics(idCarnetSante);
      fetchSigneVitaux(idCarnetSante);
      getMedicalConsultationsByPatientId(idCarnetSante);
      // ... charger autres données
    }
  }, [idCarnetSante]);

  const handleSubmit = async (data: Omit<CarnetSante, "id">) => {
    await createCarnetSante(data);
    seIsSubmite(true);
  };

  return {
    idCarnetSante: idCarnetSante,
    loading,
    data: {
      allergie: allergies,
      medicament: medicaments,
      affectation_medical: affectationMedicales,
      dispositif_medicaux: dispositifMedicaux,
      antecedant_chirurgicaux: antecedantChirurgicaux,
      antecedant_familliaux: antecedantFamiliaux,
      antecedant_sociaux: antecedentSociaux,
      vaccination: vaccinations,
      condition_gynecologique: conditionGynecologique,
      antecedent_grossesse: antecedentGrossesse,
      diagnostic: diagnostics,
      // ...
    } as Partial<CarnetSanteDTO>,
    signeVitaux: signeVitaux,
    consultations: consultations,
    facturations: listeFacturationPatient,
    handleSubmit,
  };
};
