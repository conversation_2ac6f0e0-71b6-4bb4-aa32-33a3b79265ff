import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useMedicament } from "./useMedicament";
import { useAffectationMedicale } from "./useAffectationMedicale";
import { useDispositifMedicaux } from "./useDispositifMedicaux";
import { useAntecedantChirurgicaux } from "./useAntecedantChirurgicaux";
import { useAntecedantFamiliaux } from "./useAntecedantFamiliaux";
import { useAntecedentSociaux } from "./useAntecedentSociaux";
import { useVaccination } from "./useVaccination";
import { useAllergie } from "./useAllergie";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useHistoriqueCarnetSante } from "./useHistoriqueCarnetSante";
import { useCarnetSante } from "./useCarnetSante";
import { action_carnet_de_sante_enum } from "@/domain/models/enums";
import { useAntecedentGrossesse } from "./useAntecedentGrossesse";
import { useConditionGynecologique } from "./useConditionGynecologique";
import { getTableNameByTitle } from "@/shared/utils/getTableNameByTitle";
import { useDiagnostic } from "./useDiagnostic";

export const useCreateMedicalRecord = () => {
  const { allergieState, create: createAllergie } = useAllergie();
  const { medicamentState, create: createMedicament } = useMedicament();
  const { affectationMedicaleState, create: createAffectationMedicale } = useAffectationMedicale();
  const { dispositifMedicauxState, create: createDispositifMedicaux } = useDispositifMedicaux();
  const { antecedantChirurgicauxState, create: createAntecedantChirurgicaux } = useAntecedantChirurgicaux();
  const { antecedantFamiliauxState, create: createAntecedantFamiliaux } = useAntecedantFamiliaux();
  const { antecedentSociauxState, create: createAntecedentSociaux } = useAntecedentSociaux();
  const { vaccinationState, create: createVaccination } = useVaccination();
  const { antecedentGrossesseState, create: createAntecedentGrossesse } = useAntecedentGrossesse();
  const { conditionGynecologiqueState, create: createConditionGynecologique } = useConditionGynecologique();
  const { diagnosticState, create: createDiagnostic } = useDiagnostic();
  const { create: createHistoriqueCarnetSante } = useHistoriqueCarnetSante();
  const { idCarnetSante } = useSelector((state: RootState) => state.carnetSante)

  const { selectedSearch } = useCarnetSante();

  const handleCreate = async (type: string, selectedFile: File | null) => {
    if (!idCarnetSante) return;
    const selectedItems = selectedSearch.map(item => ({ item }));

    let item: {
      id: number;
      detail: string;
    }[] | null = null;
    let tableConcernee: string | null = null;
    switch (type) {
      case TITRES_CARNET_DE_SANTE.allergies: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          nom: d.item,
          remarques: allergieState.remarks[d.item],
          reaction: Object.entries(allergieState.selectedReactions[d.item] || {})
            .filter(([_, checked]) => checked)
            .map(([reaction]) => reaction)
            .join(', '),
          confidentialite: false,
        }));
        item = await createAllergie(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.allergies);
        break;
      }
      case TITRES_CARNET_DE_SANTE.medicaments:{
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          nom: d.item,
          force: medicamentState.force[d.item],
          posologie: medicamentState.posologie[d.item],
          duree_jour: medicamentState.duree[d.item],
          type_consommation: medicamentState.typeConsommation[d.item],
          frequence_dose: medicamentState.frequence[d.item],
          quantite_par_dosage: medicamentState.quantite[d.item],
          remarques: medicamentState.remarks[d.item],
          calendrier_de_dose: medicamentState.calendrier[d.item],
          confidentialite: false,
        }));
        item = await createMedicament(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.medicaments);
        break;}
      case TITRES_CARNET_DE_SANTE.affectationMedicales: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          maladie: d.item,
          date: affectationMedicaleState.dateAcquisition[d.item],
          confidentialite: false,
          remarques: affectationMedicaleState.remarks[d.item],
        }));
        item = await createAffectationMedicale(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.affectationMedicales);
        break;
      }
      case TITRES_CARNET_DE_SANTE.dispositifMedicaux: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          nom: d.item,
          marque: dispositifMedicauxState.marque[d.item],
          modele: dispositifMedicauxState.modele[d.item],
          reference_appareil: dispositifMedicauxState.reference[d.item],
          date_acquisition: new Date(dispositifMedicauxState.dateAcquisition[d.item]),
          prochaine_mise_a_jour: new Date(dispositifMedicauxState.prochaineDate[d.item]),
          remarques: dispositifMedicauxState.remarks[d.item],
          confidentialite: false,
        }));
        item = await createDispositifMedicaux(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.dispositifMedicaux);
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          nom: d.item,
          date: new Date(antecedantChirurgicauxState.dateDeChirurgie[d.item]),
          description: antecedantChirurgicauxState.descriptions[d.item],
          confidentialite: false,
          remarques: antecedantChirurgicauxState.remarks[d.item],
        }));
        item = await createAntecedantChirurgicaux(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.antecedantChirurgicaux);
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedantFamiliaux: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          nom_lien: d.item,
          decede: antecedantFamiliauxState.decede[d.item],
          affections_medicales: antecedantFamiliauxState.affection[d.item],
          confidentialite: false,
          remarques: antecedantFamiliauxState.remarks[d.item],
        }));
        item = await createAntecedantFamiliaux(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.antecedantFamiliaux);
        break;
      }
      // case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
      //   if (!isAntecedentSociauxData(data)) {
      //     throw new Error("Invalid data format for antecedentSociaux");
      //   }
      //   await createAntecedentSociaux(data);
      //   break;
      case TITRES_CARNET_DE_SANTE.vaccination: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          nom_vaccin: d.item,
          date_administration: new Date(vaccinationState.dateAdministration[d.item]),
          prochaine_date_echeance: new Date(vaccinationState.prochaineDate[d.item]),
          confidentialite: false,
          remarques: vaccinationState.remarks[d.item],
        }));
        item = await createVaccination(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.vaccination);
        break;
      }
      case TITRES_CARNET_DE_SANTE.antecedentGrossesse: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          parite: d.item,
          date: new Date(antecedentGrossesseState.date[d.item]),
          est_enceinte: antecedentGrossesseState.estEnceinte[d.item],
          nombre_enfants: antecedentGrossesseState.nombreEnfants[d.item],
          confidentialite: false,
          remarques: antecedentGrossesseState.remarks[d.item],
        }));
        item = await createAntecedentGrossesse(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.antecedentGrossesse);
        break;
      }
      case TITRES_CARNET_DE_SANTE.conditionGynecologique: {
        const data = selectedItems.map(d => ({
          id_carnet: idCarnetSante,
          maladie: d.item,
          date: new Date(conditionGynecologiqueState.date[d.item]),
          confidentialite: false,
          remarques: conditionGynecologiqueState.remarks[d.item],
        }));
        item = await createConditionGynecologique(data);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.conditionGynecologique);
        break;
      }
      case TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage: {
        const data = {
          id_carnet: idCarnetSante,
          titre: diagnosticState.titre,
          type_fichier: diagnosticState.type_fichier,
          date: (new Date()).toISOString(),
          resultat: diagnosticState.impression_resultat,
          remarque: diagnosticState.remarks,
        };
        item = await createDiagnostic(data, selectedFile);
        tableConcernee = getTableNameByTitle(TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage);
        break;
      }
      default:
        break;
    }
    if (item) {
      // creer l'historique
      createHistoriqueCarnetSante(
        idCarnetSante,
        item,
        tableConcernee,
        action_carnet_de_sante_enum.ajout
      );
    }
  };

  return { handleCreate };
};
