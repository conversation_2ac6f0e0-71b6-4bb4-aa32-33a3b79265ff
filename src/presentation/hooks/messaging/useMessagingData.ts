import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAppSelector } from '@/presentation/hooks/redux';
import { useToast } from '@/presentation/hooks/use-toast';
import {
  Conversation,
  Message,
  Contact,
  MessagingDataHook,
  MessageSearchFilters,
} from '@/presentation/types/message.types';
import {
  MESSAGE_ERROR_MESSAGES,
  MESSAGE_SUCCESS_MESSAGES,
  PAGINATION_CONSTANTS,
} from '@/presentation/constants/message.constants';

/**
 * Custom hook for managing messaging data and operations
 * Follows the useDashboardData pattern established in the codebase
 * 
 * @returns {MessagingDataHook} Messaging data and operations
 */
export const useMessagingData = (): MessagingDataHook => {
  // State management
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversationState] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchFilters, setSearchFilters] = useState<MessageSearchFilters>({});

  // Get current user data from Redux store
  const currentUser = useAppSelector((state) => state.authentification.userData);
  const authData = useAppSelector((state) => state.authentification.authData);
  const toast = useToast();

  /**
   * Mock data for development - Replace with actual API calls
   */
  const mockConversations: Conversation[] = useMemo(() => [
    {
      id: '1',
      type: 'direct',
      participantName: 'Dr. Marie Dubois',
      participants: [
        {
          id: 'prof-1',
          name: 'Dr. Marie Dubois',
          role: 'professional',
          isOnline: true,
        },
      ],
      lastMessage: 'Votre prochain rendez-vous est confirmé pour demain à 14h.',
      lastMessageTime: '10:30',
      unreadCount: 2,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15T10:30:00'),
    },
    {
      id: '2',
      type: 'direct',
      participantName: 'Jean Martin',
      participants: [
        {
          id: 'patient-1',
          name: 'Jean Martin',
          role: 'patient',
          isOnline: false,
          lastSeen: new Date('2024-01-14T18:00:00'),
        },
      ],
      lastMessage: 'Merci pour les résultats de mes analyses.',
      lastMessageTime: 'Hier',
      unreadCount: 0,
      createdAt: new Date('2024-01-14'),
      updatedAt: new Date('2024-01-14T18:00:00'),
    },
    {
      id: '3',
      type: 'direct',
      participantName: 'Dr. Pierre Leroy',
      participants: [
        {
          id: 'prof-2',
          name: 'Dr. Pierre Leroy',
          role: 'professional',
          isOnline: true,
        },
      ],
      lastMessage: 'Pouvez-vous me transmettre le dossier du patient ?',
      lastMessageTime: '08:45',
      unreadCount: 1,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15T08:45:00'),
    },
  ], []);

  const mockMessages: Message[] = useMemo(() => [
    {
      id: 'msg-1',
      conversationId: '1',
      senderId: 'prof-1',
      senderName: 'Dr. Marie Dubois',
      senderRole: 'professional',
      content: 'Bonjour, comment allez-vous aujourd\'hui ?',
      type: 'text',
      status: 'read',
      sentAt: new Date('2024-01-15T09:00:00'),
      readAt: new Date('2024-01-15T09:05:00'),
      isOwn: false,
    },
    {
      id: 'msg-2',
      conversationId: '1',
      senderId: 'current-user-id',
      senderName: 'Moi',
      senderRole: 'patient',
      content: 'Bonjour docteur, je vais bien merci. J\'ai quelques questions concernant mon traitement.',
      type: 'text',
      status: 'read',
      sentAt: new Date('2024-01-15T09:10:00'),
      readAt: new Date('2024-01-15T09:12:00'),
      isOwn: true,
    },
    {
      id: 'msg-3',
      conversationId: '1',
      senderId: 'prof-1',
      senderName: 'Dr. Marie Dubois',
      senderRole: 'professional',
      content: 'Votre prochain rendez-vous est confirmé pour demain à 14h.',
      type: 'text',
      status: 'delivered',
      sentAt: new Date('2024-01-15T10:30:00'),
      deliveredAt: new Date('2024-01-15T10:30:30'),
      isOwn: false,
    },
  ], []);

  const mockContacts: Contact[] = useMemo(() => [
    {
      id: 'prof-1',
      name: 'Dr. Marie Dubois',
      role: 'professional',
      email: '<EMAIL>',
      specialty: 'Cardiologie',
      isOnline: true,
    },
    {
      id: 'prof-2',
      name: 'Dr. Pierre Leroy',
      role: 'professional',
      email: '<EMAIL>',
      specialty: 'Neurologie',
      isOnline: true,
    },
    {
      id: 'patient-1',
      name: 'Jean Martin',
      role: 'patient',
      email: '<EMAIL>',
      isOnline: false,
    },
    {
      id: 'patient-2',
      name: 'Sophie Durand',
      role: 'patient',
      email: '<EMAIL>',
      isOnline: true,
    },
  ], []);

  /**
   * Load conversations from API
   */
  const loadConversations = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      // const response = await conversationAPI.getByUserId(currentUser.id);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setConversations(mockConversations);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id, mockConversations, toast]);

  /**
   * Load messages for a specific conversation
   */
  const loadMessages = useCallback(async (conversationId: string) => {
    setLoading(true);
    setError(null);

    try {
      // TODO: Replace with actual API call
      // const response = await messageAPI.getByConversationId(conversationId);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const conversationMessages = mockMessages.filter(msg => msg.conversationId === conversationId);
      setMessages(conversationMessages);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [mockMessages, toast]);

  /**
   * Load available contacts
   */
  const loadContacts = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      // TODO: Replace with actual API call
      // const response = await contactAPI.getByUserId(currentUser.id);
      
      setContacts(mockContacts);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [currentUser?.id, mockContacts, toast]);

  /**
   * Set active conversation and load its messages
   */
  const setActiveConversation = useCallback(async (conversationId: string) => {
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (!conversation) {
      toast.error(MESSAGE_ERROR_MESSAGES.conversationNotFound);
      return;
    }

    setActiveConversationState(conversation);
    await loadMessages(conversationId);
  }, [conversations, loadMessages, toast]);

  /**
   * Send a message
   */
  const sendMessage = useCallback(async (
    conversationId: string,
    content: string,
    attachments?: File[]
  ) => {
    if (!content.trim()) {
      toast.error(MESSAGE_ERROR_MESSAGES.messageEmpty);
      return;
    }

    if (!currentUser?.id) {
      toast.error(MESSAGE_ERROR_MESSAGES.permissionDenied);
      return;
    }

    setLoading(true);

    try {
      // TODO: Replace with actual API call
      // const response = await messageAPI.send({
      //   conversationId,
      //   content,
      //   senderId: currentUser.id,
      //   attachments
      // });

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Create mock message
      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        conversationId,
        senderId: currentUser.id.toString(),
        senderName: `${currentUser.nom} ${currentUser.prenom || ''}`.trim(),
        senderRole: 'patient', // This should be determined from user data
        content,
        type: 'text',
        status: 'sent',
        sentAt: new Date(),
        isOwn: true,
      };

      setMessages(prev => [...prev, newMessage]);
      toast.success(MESSAGE_SUCCESS_MESSAGES.messageSent);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.sendFailed;
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentUser, toast]);

  /**
   * Mark conversation as read
   */
  const markAsRead = useCallback(async (conversationId: string) => {
    try {
      // TODO: Replace with actual API call
      // await messageAPI.markAsRead(conversationId);

      // Update local state
      setConversations(prev =>
        prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, unreadCount: 0 }
            : conv
        )
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      toast.error(errorMessage);
    }
  }, [toast]);

  /**
   * Search conversations
   */
  const searchConversations = useCallback((query: string) => {
    setSearchFilters(prev => ({ ...prev, query }));
  }, []);

  /**
   * Create new conversation
   */
  const createNewConversation = useCallback(async (contactId: string) => {
    const contact = contacts.find(c => c.id === contactId);
    if (!contact) {
      toast.error(MESSAGE_ERROR_MESSAGES.contactNotFound);
      return;
    }

    try {
      // TODO: Replace with actual API call
      // const response = await conversationAPI.create({
      //   participantIds: [currentUser.id, contactId]
      // });

      // Create mock conversation
      const newConversation: Conversation = {
        id: `conv-${Date.now()}`,
        type: 'direct',
        participantName: contact.name,
        participants: [
          {
            id: contact.id,
            name: contact.name,
            role: contact.role,
            isOnline: contact.isOnline,
          },
        ],
        lastMessage: '',
        lastMessageTime: 'Maintenant',
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setConversations(prev => [newConversation, ...prev]);
      setActiveConversationState(newConversation);
      setMessages([]);
      toast.success(MESSAGE_SUCCESS_MESSAGES.conversationCreated);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      toast.error(errorMessage);
    }
  }, [contacts, currentUser, toast]);

  /**
   * Archive conversation
   */
  const archiveConversation = useCallback(async (conversationId: string) => {
    try {
      // TODO: Replace with actual API call
      // await conversationAPI.archive(conversationId);

      setConversations(prev =>
        prev.map(conv =>
          conv.id === conversationId
            ? { ...conv, isArchived: true }
            : conv
        )
      );
      toast.success(MESSAGE_SUCCESS_MESSAGES.conversationArchived);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      toast.error(errorMessage);
    }
  }, [toast]);

  /**
   * Delete conversation
   */
  const deleteConversation = useCallback(async (conversationId: string) => {
    try {
      // TODO: Replace with actual API call
      // await conversationAPI.delete(conversationId);

      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
      if (activeConversation?.id === conversationId) {
        setActiveConversationState(null);
        setMessages([]);
      }
      toast.success(MESSAGE_SUCCESS_MESSAGES.conversationDeleted);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : MESSAGE_ERROR_MESSAGES.loadFailed;
      toast.error(errorMessage);
    }
  }, [activeConversation?.id, toast]);

  /**
   * Refresh conversations
   */
  const refreshConversations = useCallback(() => {
    loadConversations();
  }, [loadConversations]);

  // Filter conversations based on search
  const filteredConversations = useMemo(() => {
    if (!searchFilters.query) return conversations;
    
    const query = searchFilters.query.toLowerCase();
    return conversations.filter(conv =>
      conv.participantName.toLowerCase().includes(query) ||
      conv.lastMessage?.toLowerCase().includes(query)
    );
  }, [conversations, searchFilters.query]);

  // Load initial data
  useEffect(() => {
    if (currentUser?.id) {
      loadConversations();
      loadContacts();
    }
  }, [currentUser?.id, loadConversations, loadContacts]);

  return {
    conversations: filteredConversations,
    activeConversation,
    messages,
    contacts,
    loading,
    error,
    setActiveConversation,
    sendMessage,
    markAsRead,
    searchConversations,
    createNewConversation,
    archiveConversation,
    deleteConversation,
    refreshConversations,
  };
};
