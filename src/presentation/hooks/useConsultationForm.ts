import { useState } from "react";
import { consultation_medical } from "@/domain/models";

export const useConsultationForm = (
  professionalId?: number,
  carnetId?: number
) => {
  const [raisonsVisite, setRaisonsVisite] = useState("");
  const [plaintePrincipale, setPlaintePrincipale] = useState("");
  const [remarques, setRemarques] = useState("");
  const [examenSystemes, setExamenSystemes] = useState("");
  const [diagnostic, setDiagnostic] = useState("");
  const [planSoins, setPlanSoins] = useState("");

  const isFormValid = () => {
    return raisonsVisite && remarques && diagnostic && planSoins;
  };

  const initialiseState = (consultation: consultation_medical) => {
    setRaisonsVisite(consultation.raison_de_visite);
    setPlaintePrincipale(consultation.plainte_principale);
    setRemarques(consultation.remarque);
    setExamenSystemes(consultation.examen_systemes);
    setDiagnostic(consultation.diagnostique);
    setPlanSoins(consultation.plan_de_soin);
  };

  const getConsultationData = (): Omit<consultation_medical, "id"> => ({
    id_professionnel: professionalId || 0,
    id_carnet: carnetId,
    date_visite: new Date(),
    raison_de_visite: raisonsVisite,
    plainte_principale: plaintePrincipale,
    remarque: remarques,
    examen_systemes: examenSystemes,
    diagnostique: diagnostic,
    plan_de_soin: planSoins,
    confidentialite: false,
  });

  const resetForm = () => {
    setRaisonsVisite("");
    setPlaintePrincipale("");
    setRemarques("");
    setExamenSystemes("");
    setDiagnostic("");
    setPlanSoins("");
  };

  return {
    formData: {
      raisonsVisite,
      plaintePrincipale,
      remarques,
      examenSystemes,
      diagnostic,
      planSoins,
      setRaisonsVisite,
      setPlaintePrincipale,
      setRemarques,
      setExamenSystemes,
      setDiagnostic,
      setPlanSoins,
    },
    isFormValid: isFormValid(),
    getConsultationData,
    resetForm,
    initialiseState,
  };
};
