import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import * as constants from "./constants";
import { useCarnetSanteData } from "@/presentation/hooks/useCarnetSanteData";
import { getContentForType } from "@/shared/utils/getContentForType";

export const useOptionsCarnetDeSante = (type: string) => {
  const { data } = useCarnetSanteData();
  let options = [];
  // Get existing items for the given type
  const existingItems = getContentForType(type, data);

  // Get all available options for the type
  switch (type) {
    case TITRES_CARNET_DE_SANTE.allergies:
      options = constants.AllergyOptions;
      break;
    case TITRES_CARNET_DE_SANTE.medicaments:
      options = constants.MedicamentsOptions;
      break;
    case TITRES_CARNET_DE_SANTE.affectationMedicales:
      options = constants.AffectationMedicalesOptions;
      break;
    case TITRES_CARNET_DE_SANTE.dispositifMedicaux:
      options = constants.DispositifMedicauxOptions;
      break;
    case TITRES_CARNET_DE_SANTE.antecedantChirurgicaux:
      options = constants.AntecedantChirurgicauxOptions;
      break;
    case TITRES_CARNET_DE_SANTE.antecedantFamiliaux:
      options = constants.AntecedantFamiliauxOptions;
      break;
    case TITRES_CARNET_DE_SANTE.antecedentsSociaux:
      options = constants.AntecedantSociauxOptions;
      break;
    case TITRES_CARNET_DE_SANTE.vaccination:
      options = constants.VaccinationsOptions;
      break;
    case TITRES_CARNET_DE_SANTE.antecedentGrossesse:
      options = constants.AntecedentGrossesseOptions;
      break;
    case TITRES_CARNET_DE_SANTE.conditionGynecologique:
      options = constants.ConditionGynecologiqueOptions;
      break;
    default:
      return [];
  }

  // Filter out options that already exist in the patient's data
  return options.filter((option) => !existingItems.includes(option));
};
