import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AntecedentGrossesse } from '@/domain/models'
import { AppDispatch, RootState } from '@/store'
import {
  createAntecedentGrossesse,
  getAllAntecedentGrossesse,
  updateAntecedentGrossesse,
  deleteAntecedentGrossesse,
  setSelectedAntecedentGrossesse,
  setDate,
  setEstEnceinte,
  setNombreEnfants,
  setParite,
  setRemarks,
  resetAntecedentGrossesseState,
  clearSelectedAntecedentGrossesse
} from '@/application/slices/professionnal/antecedentGrossesseSlice'
import { getLocalISOString } from '@/shared/utils/getLocalISOString'

export const useAntecedentGrossesse = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    antecedentGrossesse,
    selectedAntecedentGrossesse,
    antecedentGrossesseState,
    loading,
    error
  } = useSelector((state: RootState) => state.antecedentGrossesse)

  const create = useCallback(
    async (data: Omit<AntecedentGrossesse, "id">[]) => {
      const register = await dispatch(createAntecedentGrossesse(data))
      const antecedants = register.payload as AntecedentGrossesse[]
      return antecedants.map(antecedant => {
        return {
          id: antecedant.id,
          detail: antecedant.parite
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getAllAntecedentGrossesse(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<AntecedentGrossesse>) => {
      await dispatch(updateAntecedentGrossesse({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteAntecedentGrossesse(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (antecedentGrossesse: AntecedentGrossesse | null) => {
      dispatch(setSelectedAntecedentGrossesse(antecedentGrossesse))
    },
    [dispatch]
  )

  const handleNombreEnfantsChange = (item: string, value: number) => {
    console.log(value);
    const newValue = Number(value)
    dispatch(setNombreEnfants({item, value: newValue}))
  };
  
  const handleEstEnceinteChange = (item: string, value: string) => {
    const newValue = value === "oui" ? true : false
    dispatch(setEstEnceinte({item, value: newValue}))
  };

  const handlePariteChange = (item: string, value: string) => {
    dispatch(setParite({item, value}))
  };

  const handleDateChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setDate({item, value: getLocalISOString(value)}));
  };
  
  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetAntecedentGrossesseState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedAntecedentGrossesse())
  }, [dispatch])

  return {
    antecedentGrossesse,
    selectedAntecedentGrossesse,
    antecedentGrossesseState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleEstEnceinteChange,
    handlePariteChange,
    handleDateChange,
    handleNombreEnfantsChange,
    handleRemarksChange,
    resetState,
    clearSelected
  }
}
