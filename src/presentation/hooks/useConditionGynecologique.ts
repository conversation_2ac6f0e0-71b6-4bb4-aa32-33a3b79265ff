import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { ConditionGynecologique } from '@/domain/models'
import { AppDispatch, RootState } from '@/store'
import {
  createConditionGynecologique,
  getAllConditionGynecologique,
  updateConditionGynecologique,
  deleteConditionGynecologique,
  setSelectedConditionGynecologique,
  setDate,
  setMaladie,
  setRemarks,
  resetConditionGynecologiqueState,
  clearSelectedConditionGynecologique
} from '@/application/slices/professionnal/conditionGynecologiqueSlice'
import { getLocalISOString } from '@/shared/utils/getLocalISOString'

export const useConditionGynecologique = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    conditionGynecologique,
    selectedConditionGynecologique,
    conditionGynecologiqueState,
    loading,
    error
  } = useSelector((state: RootState) => state.conditionGynecologiqueSlice)

  const create = useCallback(
    async (data: Omit<ConditionGynecologique, "id">[]) => {
      const register = await dispatch(createConditionGynecologique(data))
      const antecedants = register.payload as ConditionGynecologique[]
      return antecedants.map(antecedant => {
        return {
          id: antecedant.id,
          detail: antecedant.maladie
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getAllConditionGynecologique(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<ConditionGynecologique>) => {
      await dispatch(updateConditionGynecologique({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteConditionGynecologique(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (conditionGynecologique: ConditionGynecologique | null) => {
      dispatch(setSelectedConditionGynecologique(conditionGynecologique))
    },
    [dispatch]
  )

  const handleMaladieChange = (item: string, value: string) => {
    dispatch(setMaladie({item, value}))
  };

  const handleDateChange = (item: string, value: Date | null) => {
    if (!value) return;
    dispatch(setDate({item, value: getLocalISOString(value)}));
  };
  
  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetConditionGynecologiqueState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedConditionGynecologique())
  }, [dispatch])

  return {
    conditionGynecologique,
    selectedConditionGynecologique,
    conditionGynecologiqueState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleMaladieChange,
    handleDateChange,
    handleRemarksChange,
    resetState,
    clearSelected
  }
}
