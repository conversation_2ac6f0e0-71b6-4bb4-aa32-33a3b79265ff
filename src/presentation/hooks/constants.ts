import AllergyFr from "@/assets/allergies_fr.json";
import AntecedantChirurgicauxFr from "@/assets/antecedents_chirurgicaus_fr.json";
import AffectationMedicalesFr from "@/assets/medical_conditions_fr.json";
import MedicamentsFr from "@/assets/medications_fr.json";
import VaccinationsFr from "@/assets/vaccines_fr.json";
import AntecedantFamiliauxFr from "@/assets/antecedant_familiaux_fr.json";
import AntecedantSociauxFr from "@/assets/antecedant_sociaux.json";
import DispositifMedicauxFr from "@/assets/dispositif_medicaux.json";
import ConditionGynecologiqueFr from "@/assets/condition_gynecologique.json";
import AntecedentGrossesseFr from "@/assets/antecedent_grossesse.json";

export const AllergyOptions = AllergyFr;
export const MedicamentsOptions = MedicamentsFr;
export const AffectationMedicalesOptions = AffectationMedicalesFr;
export const DispositifMedicauxOptions = DispositifMedicauxFr;
export const AntecedantChirurgicauxOptions = AntecedantChirurgicauxFr;
export const AntecedantFamiliauxOptions = AntecedantFamiliauxFr;
export const AntecedantSociauxOptions = AntecedantSociauxFr;
export const VaccinationsOptions = VaccinationsFr;
export const ConditionGynecologiqueOptions = ConditionGynecologiqueFr;
export const AntecedentGrossesseOptions = AntecedentGrossesseFr;
