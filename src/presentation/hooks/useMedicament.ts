import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { Medicament } from '@/domain/models'
import {
  createMedicamentSlice,
  getAllMedicamentSlices,
  updateMedicamentSlice,
  deleteMedicamentSlice,
  setSelectedMedicamentSlice,
  setForce,
  setPosologie,
  setDuree,
  setTypeConsommation,
  setFrequence,
  setQuantite,
  setCalendrier,
  setRemarks,
  resetMedicamentState,
  clearSelectedMedicamentSlice
} from '@/application/slices/professionnal/medicamentSlice'

export const useMedicament = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    medicaments,
    selectedMedicamentSlice,
    medicamentState,
    loading,
    error
  } = useSelector((state: RootState) => state.medicament)

  const create = useCallback(
    async (data: Omit<Medicament, "id">[]) => {
      const register = await dispatch(createMedicamentSlice(data))
      const medicaments = register.payload as Medicament[]
      return medicaments.map(medicament => {
        return {
          id: medicament.id,
          detail: medicament.nom
        }
      })
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getAllMedicamentSlices(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<Medicament>) => {
      await dispatch(updateMedicamentSlice({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteMedicamentSlice(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (medicament: Medicament | null) => {
      dispatch(setSelectedMedicamentSlice(medicament))
    },
    [dispatch]
  )

  const handleForceChange = (item: string, value: string) => {
    dispatch(setForce({item, value}))
  };
  
  const handlePosologieChange = (item: string, value: string) => {
    dispatch(setPosologie({item, value}))
  };
  
  const handleDureeChange = (item: string, value: string) => {
    const newValue = Number(value)
    dispatch(setDuree({item, value: newValue}))
  };
  
  const handleTypeConsommationChange = (item: string, value: string) => {
    dispatch(setTypeConsommation({item, value}))
  };
  
  const handleFrequenceChange = (item: string, value: string) => {
    dispatch(setFrequence({item, value}))
  };
  
  const handleQuantiteChange = (item: string, value: string) => {
    dispatch(setQuantite({item, value}))
  };
  
  const handleCalendrierChange = (item: string, value: string) => {
    dispatch(setCalendrier({item, value}))
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetMedicamentState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedMedicamentSlice())
  }, [dispatch])

  return {
    medicaments,
    selectedMedicamentSlice,
    medicamentState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleForceChange,
    handlePosologieChange,
    handleDureeChange,
    handleTypeConsommationChange,
    handleFrequenceChange,
    handleQuantiteChange,
    handleCalendrierChange,
    handleRemarksChange,
    resetState,
    clearSelected
  }
}
