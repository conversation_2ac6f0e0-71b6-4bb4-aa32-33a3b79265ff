import { laboratoire_diagnostics } from "@/domain/models";
import { AppDispatch, RootState } from "@/store";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createDiagnosticSlice,
  getAllDiagnosticSlices,
  updateDiagnosticSlice,
  deleteDiagnosticSlice,
  setSelectedDiagnosticSlice,
  setTitle,
  setTypeFichier,
  setImpressionResultat,
  setPath,
  setRemarks,
  resetDiagnosticState,
  clearSelectedDiagnosticSlice,
} from "@/application/slices/professionnal/diagnosticSlice";
import { UploadRepository } from "@/infrastructure/repositories/uploadFile";
import { GetPublicUrlRepository } from "@/infrastructure/repositories/uploadFile";
import { upload_file_enum } from "@/domain/models/enums";
import { MAX_FILE_SIZE, UploadServices } from "@/domain/services/UploadService";

const uploadRepository = new UploadRepository();
const getPublicUrlRepository = new GetPublicUrlRepository();
const uploadServices = new UploadServices(
  uploadRepository,
  getPublicUrlRepository
)

export const useDiagnostic = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    diagnostics,
    diagnosticState,
    selectedDiagnosticSlice,
    loading,
    error
  } = useSelector(
    (state: RootState) => state.diagnostic
  );

  const create = useCallback(
    async (data: Omit<laboratoire_diagnostics, "id">, selectedFile: File | null) => {
      if (selectedFile) {
        if (selectedFile.size < MAX_FILE_SIZE) {
          const imageUrl = await uploadServices.execute(selectedFile, upload_file_enum.diagnostics);
          console.log(imageUrl);
          data.path = imageUrl;
        } else {
          throw new Error(
            `Le logo ne doit pas depasser la taille de ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
          );
        }
      }
      const register = await dispatch(createDiagnosticSlice(data));
      const diagnostic = register.payload as laboratoire_diagnostics;
      return [{
        id: diagnostic.id,
        detail: diagnostic.titre
      }]
    },
    [dispatch]
  );

  const getAll = useCallback(
    async (carnetId: number) => {
      await dispatch(getAllDiagnosticSlices(carnetId));
    },
    [dispatch]
  );

  const update = useCallback(
    async (id: number, data: Partial<laboratoire_diagnostics>, selectedFile: File | null) => {
      await dispatch(updateDiagnosticSlice({ id, data, selectedFile }));
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteDiagnosticSlice(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (diagnostic: laboratoire_diagnostics | null) => {
      dispatch(setSelectedDiagnosticSlice(diagnostic));
    },
    [dispatch]
  );

  const handleTitleChange = (value: string) => {
    dispatch(setTitle(value))
  };

  const handleTypeFichierChange = (value: string) => {
    dispatch(setTypeFichier(value))
  };

  const handlePathChange = (value: string) => {
    dispatch(setPath(value))
  };

  const handleImpressionResultatChange = (value: string) => {
    dispatch(setImpressionResultat(value))
  };

  const handleRemarksChange = (value: string) => {
    dispatch(setRemarks(value))
  };
  
  const resetState = () => {
    dispatch(resetDiagnosticState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedDiagnosticSlice());
  }, [dispatch]);

  return {
    loading,
    error,
    diagnostics,
    selectedDiagnosticSlice,
    diagnosticState,
    create,
    select,
    getAll,
    update,
    remove,
    clearSelected,
    handleTitleChange,
    handleTypeFichierChange,
    handleImpressionResultatChange,
    handlePathChange,
    handleRemarksChange,
    resetState,
  };
};
