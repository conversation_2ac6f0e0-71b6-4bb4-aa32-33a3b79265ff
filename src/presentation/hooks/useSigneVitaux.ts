import { useAppDispatch, useAppSelector } from "./redux";
import { signe_vitaux } from "@/domain/models/SigneVitaux";
import {
  getAllSigneVitaux,
  createSigneVitaux,
  updateSigneVitaux,
  deleteSigneVitaux,
} from "@/application/slices/professionnal/signeVitauxSlice";
import { useCallback } from "react";

export const useSigneVitaux = () => {
  const dispatch = useAppDispatch();
  const { signeVitaux, loading, error } = useAppSelector((state) => state.signeVitaux);

  const getAll = useCallback(
    async (carnetId: number) => {
      if (!navigator.onLine) {
        throw new Error(
          'Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.'
        )
      }
      await dispatch(getAllSigneVitaux(carnetId)).unwrap()
    },
    [dispatch]
  )

  const create = async (data: Omit<signe_vitaux, "id">) => {
    try {
      const result = await dispatch(createSigneVitaux(data)).unwrap();
      return result;
    } catch (error) {
      console.error("Error creating signe vital:", error);
      throw error;
    }
  };

  const update = async (id: number, data: Partial<signe_vitaux>) => {
    try {
      const result = await dispatch(updateSigneVitaux({ id, data })).unwrap();
      return result;
    } catch (error) {
      console.error("Error updating signe vital:", error);
      throw error;
    }
  };

  const remove = async (id: number) => {
    try {
      await dispatch(deleteSigneVitaux(id)).unwrap();
    } catch (error) {
      console.error("Error deleting signe vital:", error);
      throw error;
    }
  };

  return {
    signeVitaux,
    loading,
    error,
    getAll,
    create,
    update,
    remove,
  };
};
