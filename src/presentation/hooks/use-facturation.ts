import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "./redux";
import {
  fetchFacturationById,
  fetchFacturationsByProfessionalId,
  fetchFacturationsByPatientId,
  createFacturation,
  updateFacturation,
  deleteFacturation,
  clearCurrentFacturation,
  clearError,
} from "@/application/slices/professionnal/facturationSlice";
import { Facturation } from "@/domain/models";

export const useFacturation = () => {
  const dispatch = useAppDispatch();
  const {
    listeFacturationPatient,
    listeFacturationProfessional,
    currentFacturation,
    loading,
    error,
  } = useAppSelector((state) => state.facturation);

  const getFacturationById = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchFacturationById(id)).unwrap();
    },
    [dispatch]
  );

  const getFacturationsByProfessionalId = useCallback(
    async (professionalId: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(
        fetchFacturationsByProfessionalId(professionalId)
      ).unwrap();
    },
    [dispatch]
  );

  const getFacturationsByPatientId = useCallback(
    async (patientId: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      await dispatch(fetchFacturationsByPatientId(patientId)).unwrap();
    },
    [dispatch]
  );

  const handleCreateFacturation = useCallback(
    async (facturationData: Omit<Facturation, "id">) => {
      const result = await dispatch(
        createFacturation(facturationData)
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleUpdateFacturation = useCallback(
    async (id: number, facturationData: Partial<Facturation>) => {
      const result = await dispatch(
        updateFacturation({ id, facturation: facturationData })
      ).unwrap();
      return result;
    },
    [dispatch]
  );

  const handleDeleteFacturation = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer."
        );
      }
      const result = await dispatch(deleteFacturation(id)).unwrap();
      return result;
    },
    [dispatch]
  );

  return {
    listeFacturationPatient,
    listeFacturationProfessional,
    currentFacturation,
    loading,
    error,
    getFacturationById,
    getFacturationsByProfessionalId,
    getFacturationsByPatientId,
    handleCreateFacturation,
    handleUpdateFacturation,
    handleDeleteFacturation,
  };
};
