import { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch, RootState } from '@/store'
import { Antecedant_sociaux } from '@/domain/models'
import {
  createAntecedentSociaux,
  getAntecedentSociaux,
  updateAntecedentSociaux,
  deleteAntecedentSociaux,
  setSelectedAntecedentSociaux,
  setEstActive,
  setQualite,
  setRemarks,
  resetAntecedentSociauxState,
  clearSelectedAntecedentSociaux
} from '@/application/slices/professionnal/antecedentSociauxSlice'

export const useAntecedentSociaux = () => {
  const dispatch = useDispatch<AppDispatch>()
  const {
    antecedentSociaux,
    selectedAntecedentSociaux,
    antecedentSociauxState,
    loading,
    error
  } = useSelector((state: RootState) => state.antecedentSociaux)

  const create = useCallback(
    async (data: Antecedant_sociaux) => {
      await dispatch(createAntecedentSociaux(data))
    },
    [dispatch]
  )

  const getAll = useCallback(async (carnetId: number) => {
    await dispatch(getAntecedentSociaux(carnetId))
  }, [dispatch])

  const update = useCallback(
    async (id: number, data: Partial<Antecedant_sociaux>) => {
      await dispatch(updateAntecedentSociaux({ id, data }))
    },
    [dispatch]
  )

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteAntecedentSociaux(id))
    },
    [dispatch]
  )

  const select = useCallback(
    (antecedentSociaux: Antecedant_sociaux | null) => {
      dispatch(setSelectedAntecedentSociaux(antecedentSociaux))
    },
    [dispatch]
  )

  const handleEstActiveChange = (item: string, value: string) => {
    dispatch(setEstActive({item, value}))
  };
  
  const handltQualiteChange = (item: string, value: string) => {
    dispatch(setQualite({item, value}))
  };

  const handleRemarksChange = (item: string, value: string) => {
    dispatch(setRemarks({item, value}))
  };

  const resetState = () => {
    dispatch(resetAntecedentSociauxState())
  };

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedAntecedentSociaux())
  }, [dispatch])

  return {
    antecedentSociaux,
    selectedAntecedentSociaux,
    antecedentSociauxState,
    loading,
    error,
    create,
    getAll,
    update,
    remove,
    select,
    handleEstActiveChange,
    handltQualiteChange,
    handleRemarksChange,
    resetState,
    clearSelected
  }
}
