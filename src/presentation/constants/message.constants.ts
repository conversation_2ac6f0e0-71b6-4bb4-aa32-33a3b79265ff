import { MessageStatus, MessageType, UserRole } from '@/presentation/types/message.types';

/**
 * Message status styling constants
 */
export const MESSAGE_STATUS_STYLES = {
  sent: {
    icon: 'check',
    color: 'text-gray-400 dark:text-gray-500',
    label: 'Envoyé',
  },
  delivered: {
    icon: 'check-check',
    color: 'text-gray-400 dark:text-gray-500',
    label: 'Livré',
  },
  read: {
    icon: 'check-check',
    color: 'text-meddoc-primary',
    label: 'Lu',
  },
  failed: {
    icon: 'x-circle',
    color: 'text-red-500',
    label: 'Échec',
  },
} as const;

/**
 * Message type styling constants
 */
export const MESSAGE_TYPE_STYLES = {
  text: {
    icon: 'message-circle',
    bgColor: 'bg-white dark:bg-gray-700',
    borderColor: 'border-gray-200 dark:border-gray-600',
  },
  image: {
    icon: 'image',
    bgColor: 'bg-blue-50 dark:bg-blue-950',
    borderColor: 'border-blue-200 dark:border-blue-800',
  },
  file: {
    icon: 'file',
    bgColor: 'bg-gray-50 dark:bg-gray-800',
    borderColor: 'border-gray-200 dark:border-gray-600',
  },
  system: {
    icon: 'info',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
  },
} as const;

/**
 * User role styling constants for messaging
 */
export const USER_ROLE_STYLES = {
  patient: {
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900',
    label: 'Patient',
    icon: 'user',
  },
  professional: {
    color: 'text-meddoc-primary',
    bgColor: 'bg-meddoc-primary/10',
    label: 'Professionnel',
    icon: 'stethoscope',
  },
  admin: {
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-100 dark:bg-purple-900',
    label: 'Administrateur',
    icon: 'shield',
  },
} as const;

/**
 * Message bubble styling constants
 */
export const MESSAGE_BUBBLE_STYLES = {
  own: {
    container: 'flex justify-end',
    bubble: 'bg-meddoc-primary text-white rounded-l-lg rounded-tr-lg max-w-xs lg:max-w-md',
    tail: 'border-l-meddoc-primary',
  },
  other: {
    container: 'flex justify-start',
    bubble: 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-r-lg rounded-tl-lg max-w-xs lg:max-w-md',
    tail: 'border-r-gray-100 dark:border-r-gray-700',
  },
} as const;

/**
 * Conversation status styling constants
 */
export const CONVERSATION_STATUS_STYLES = {
  active: {
    container: 'bg-meddoc-primary/10 border-meddoc-primary/20',
    indicator: 'bg-meddoc-primary',
  },
  unread: {
    container: 'bg-blue-50 dark:bg-blue-950 border-blue-100 dark:border-blue-900',
    indicator: 'bg-blue-500',
  },
  archived: {
    container: 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700',
    indicator: 'bg-gray-400',
  },
  muted: {
    container: 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 opacity-60',
    indicator: 'bg-gray-300',
  },
} as const;

/**
 * Online status styling constants
 */
export const ONLINE_STATUS_STYLES = {
  online: {
    color: 'bg-green-500',
    label: 'En ligne',
  },
  offline: {
    color: 'bg-gray-400',
    label: 'Hors ligne',
  },
  away: {
    color: 'bg-yellow-500',
    label: 'Absent',
  },
  busy: {
    color: 'bg-red-500',
    label: 'Occupé',
  },
} as const;

/**
 * Message time formatting constants
 */
export const MESSAGE_TIME_FORMATS = {
  today: 'HH:mm',
  yesterday: "'Hier' HH:mm",
  thisWeek: 'EEEE HH:mm',
  older: 'dd/MM/yyyy HH:mm',
} as const;

/**
 * File size formatting constants
 */
export const FILE_SIZE_UNITS = ['B', 'KB', 'MB', 'GB'] as const;

/**
 * Maximum file upload size (5MB)
 */
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

/**
 * Allowed file types for attachments
 */
export const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
] as const;

/**
 * Message composition limits
 */
export const MESSAGE_LIMITS = {
  maxLength: 2000,
  maxAttachments: 5,
  typingTimeout: 3000, // 3 seconds
} as const;

/**
 * Pagination constants
 */
export const PAGINATION_CONSTANTS = {
  messagesPerPage: 50,
  conversationsPerPage: 20,
  contactsPerPage: 100,
} as const;

/**
 * Animation constants
 */
export const ANIMATION_CONSTANTS = {
  messageAppear: 'animate-fade-in',
  typingIndicator: 'animate-pulse',
  onlineIndicator: 'animate-pulse-slow',
  scrollDuration: 300,
} as const;

/**
 * Keyboard shortcuts
 */
export const KEYBOARD_SHORTCUTS = {
  sendMessage: 'Enter',
  sendMessageWithShift: 'Shift+Enter',
  newConversation: 'Ctrl+N',
  search: 'Ctrl+F',
  archiveConversation: 'Ctrl+A',
  deleteConversation: 'Delete',
} as const;

/**
 * Error messages for messaging
 */
export const MESSAGE_ERROR_MESSAGES = {
  sendFailed: 'Échec de l\'envoi du message. Veuillez réessayer.',
  loadFailed: 'Impossible de charger les messages.',
  connectionLost: 'Connexion perdue. Reconnexion en cours...',
  fileTooLarge: `Le fichier est trop volumineux. Taille maximale: ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
  fileTypeNotAllowed: 'Type de fichier non autorisé.',
  messageEmpty: 'Le message ne peut pas être vide.',
  messageTooLong: `Le message est trop long. Maximum: ${MESSAGE_LIMITS.maxLength} caractères.`,
  tooManyAttachments: `Trop de pièces jointes. Maximum: ${MESSAGE_LIMITS.maxAttachments}`,
  conversationNotFound: 'Conversation introuvable.',
  contactNotFound: 'Contact introuvable.',
  permissionDenied: 'Permission refusée.',
} as const;

/**
 * Success messages for messaging
 */
export const MESSAGE_SUCCESS_MESSAGES = {
  messageSent: 'Message envoyé avec succès.',
  conversationCreated: 'Nouvelle conversation créée.',
  conversationArchived: 'Conversation archivée.',
  conversationDeleted: 'Conversation supprimée.',
  messageMarkedAsRead: 'Message marqué comme lu.',
  fileUploaded: 'Fichier téléchargé avec succès.',
} as const;

/**
 * Placeholder texts
 */
export const PLACEHOLDER_TEXTS = {
  messageInput: 'Tapez votre message...',
  searchConversations: 'Rechercher une conversation...',
  searchContacts: 'Rechercher un contact...',
  noConversations: 'Aucune conversation trouvée',
  noMessages: 'Aucun message dans cette conversation',
  noContacts: 'Aucun contact disponible',
  selectConversation: 'Sélectionnez une conversation pour commencer',
  startNewConversation: 'Commencer une nouvelle conversation',
} as const;

/**
 * Accessibility labels
 */
export const ACCESSIBILITY_LABELS = {
  sendMessage: 'Envoyer le message',
  attachFile: 'Joindre un fichier',
  searchConversations: 'Rechercher dans les conversations',
  newConversation: 'Nouvelle conversation',
  markAsRead: 'Marquer comme lu',
  archiveConversation: 'Archiver la conversation',
  deleteConversation: 'Supprimer la conversation',
  onlineStatus: 'Statut en ligne',
  messageStatus: 'Statut du message',
  conversationList: 'Liste des conversations',
  messageList: 'Liste des messages',
  contactList: 'Liste des contacts',
} as const;
