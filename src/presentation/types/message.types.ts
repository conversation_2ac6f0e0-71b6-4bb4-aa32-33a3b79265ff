/**
 * Message status enumeration
 */
export type MessageStatus = 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Message type enumeration
 */
export type MessageType = 'text' | 'image' | 'file' | 'system';

/**
 * Conversation type enumeration
 */
export type ConversationType = 'direct' | 'group';

/**
 * User role in messaging context
 */
export type UserRole = 'patient' | 'professional' | 'admin';

/**
 * Individual message interface
 */
export interface Message {
  /** Unique message identifier */
  id: string;
  /** ID of the conversation this message belongs to */
  conversationId: string;
  /** ID of the user who sent the message */
  senderId: string;
  /** Name of the sender */
  senderName: string;
  /** Role of the sender */
  senderRole: UserRole;
  /** Message content */
  content: string;
  /** Type of message */
  type: MessageType;
  /** Message status */
  status: MessageStatus;
  /** Timestamp when message was sent */
  sentAt: Date;
  /** Timestamp when message was delivered */
  deliveredAt?: Date;
  /** Timestamp when message was read */
  readAt?: Date;
  /** Whether this message is from the current user */
  isOwn?: boolean;
  /** Optional attachment information */
  attachment?: MessageAttachment;
}

/**
 * Message attachment interface
 */
export interface MessageAttachment {
  /** Attachment ID */
  id: string;
  /** File name */
  name: string;
  /** File size in bytes */
  size: number;
  /** MIME type */
  type: string;
  /** Download URL */
  url: string;
  /** Thumbnail URL for images */
  thumbnailUrl?: string;
}

/**
 * Conversation participant interface
 */
export interface ConversationParticipant {
  /** User ID */
  id: string;
  /** User name */
  name: string;
  /** User role */
  role: UserRole;
  /** Avatar URL */
  avatarUrl?: string;
  /** Whether user is currently online */
  isOnline?: boolean;
  /** Last seen timestamp */
  lastSeen?: Date;
}

/**
 * Conversation interface
 */
export interface Conversation {
  /** Unique conversation identifier */
  id: string;
  /** Conversation type */
  type: ConversationType;
  /** Conversation title/name */
  title?: string;
  /** List of participants */
  participants: ConversationParticipant[];
  /** Last message in the conversation */
  lastMessage?: string;
  /** Timestamp of last message */
  lastMessageTime?: string;
  /** Name of the other participant (for direct conversations) */
  participantName: string;
  /** Number of unread messages */
  unreadCount: number;
  /** Whether conversation is archived */
  isArchived?: boolean;
  /** Whether conversation is muted */
  isMuted?: boolean;
  /** Conversation creation timestamp */
  createdAt: Date;
  /** Last activity timestamp */
  updatedAt: Date;
}

/**
 * Contact interface for user selection
 */
export interface Contact {
  /** Contact ID */
  id: string;
  /** Contact name */
  name: string;
  /** Contact role */
  role: UserRole;
  /** Contact email */
  email?: string;
  /** Contact phone */
  phone?: string;
  /** Avatar URL */
  avatarUrl?: string;
  /** Whether contact is online */
  isOnline?: boolean;
  /** Specialty (for professionals) */
  specialty?: string;
  /** Department or organization */
  department?: string;
}

/**
 * Message composition state interface
 */
export interface MessageCompositionState {
  /** Message content */
  content: string;
  /** Whether message is being sent */
  isSending: boolean;
  /** Attached files */
  attachments: File[];
  /** Error message if any */
  error?: string;
}

/**
 * Messaging data hook return type
 */
export interface MessagingDataHook {
  /** List of conversations */
  conversations: Conversation[];
  /** Currently active conversation */
  activeConversation: Conversation | null;
  /** Messages in active conversation */
  messages: Message[];
  /** Available contacts */
  contacts: Contact[];
  /** Loading state */
  loading: boolean;
  /** Error state */
  error: string | null;
  /** Set active conversation */
  setActiveConversation: (conversationId: string) => void;
  /** Send a message */
  sendMessage: (conversationId: string, content: string, attachments?: File[]) => Promise<void>;
  /** Mark conversation as read */
  markAsRead: (conversationId: string) => void;
  /** Search conversations */
  searchConversations: (query: string) => void;
  /** Create new conversation */
  createNewConversation: (contactId: string) => void;
  /** Archive conversation */
  archiveConversation: (conversationId: string) => void;
  /** Delete conversation */
  deleteConversation: (conversationId: string) => void;
  /** Refresh conversations */
  refreshConversations: () => void;
}

/**
 * Message composition hook return type
 */
export interface MessageCompositionHook {
  /** Composition state */
  state: MessageCompositionState;
  /** Update message content */
  setContent: (content: string) => void;
  /** Add attachment */
  addAttachment: (file: File) => void;
  /** Remove attachment */
  removeAttachment: (index: number) => void;
  /** Send message */
  sendMessage: () => Promise<void>;
  /** Reset composition state */
  reset: () => void;
}

/**
 * Message search filters
 */
export interface MessageSearchFilters {
  /** Search query */
  query?: string;
  /** Filter by sender */
  senderId?: string;
  /** Filter by date range */
  dateFrom?: Date;
  dateTo?: Date;
  /** Filter by message type */
  messageType?: MessageType;
  /** Filter by conversation */
  conversationId?: string;
}

/**
 * Pagination interface for messages
 */
export interface MessagePagination {
  /** Current page */
  page: number;
  /** Items per page */
  limit: number;
  /** Total items */
  total: number;
  /** Whether there are more items */
  hasMore: boolean;
}

/**
 * Real-time message event types
 */
export type MessageEventType = 
  | 'message_sent'
  | 'message_delivered'
  | 'message_read'
  | 'user_typing'
  | 'user_online'
  | 'user_offline'
  | 'conversation_created'
  | 'conversation_updated';

/**
 * Real-time message event interface
 */
export interface MessageEvent {
  /** Event type */
  type: MessageEventType;
  /** Event data */
  data: any;
  /** Timestamp */
  timestamp: Date;
  /** User who triggered the event */
  userId?: string;
  /** Conversation ID if applicable */
  conversationId?: string;
}
