import React, { memo } from "react";
import { motion } from "framer-motion";
import {
  GraduationCap,
  Briefcase,
  FileText,
  ExternalLink,
  Calendar,
} from "lucide-react";
import { ExtendedProfessionalProfile } from "@/presentation/hooks/useProfileData";

interface CredentialsSectionProps {
  professional: ExtendedProfessionalProfile | null;
}

/**
 * Section des qualifications professionnelles
 * Affiche les diplômes, expériences et publications du professionnel
 * Suit les patterns de sécurité en n'affichant que les informations publiques
 */
const CredentialsSectionComponent: React.FC<CredentialsSectionProps> = ({
  professional,
}) => {
  // Vérifier s'il y a des qualifications à afficher
  const hasCredentials = Boolean(
    professional?.diplomes?.length ||
      professional?.experiences?.length ||
      professional?.publications?.length
  );

  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      id="qualifications"
      className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 overflow-hidden relative"
    >
      {/* Effet de fond décoratif */}
      <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-meddoc-secondary/5 to-meddoc-primary/5 rounded-full -translate-y-20 -translate-x-20"></div>

      <div className="flex items-start gap-3 relative z-10">
        <div className="bg-gradient-to-br from-meddoc-secondary to-meddoc-primary p-2 rounded-lg">
          <GraduationCap className="h-5 w-5 text-white" />
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold mb-6 text-meddoc-fonce">
            Qualifications professionnelles
          </h2>

          {/* Message si aucune qualification */}
          {!hasCredentials && (
            <div className="text-center py-8 text-gray-500">
              <GraduationCap className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="text-lg font-medium mb-2">
                Aucune qualification renseignée
              </p>
              <p className="text-sm">
                Les informations sur les diplômes, expériences et publications
                ne sont pas encore disponibles pour ce professionnel.
              </p>
            </div>
          )}

          {/* Diplômes */}
          {professional?.diplomes && professional.diplomes.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <GraduationCap className="h-4 w-4 text-meddoc-primary" />
                Diplômes et formations
              </h3>
              <div className="space-y-4">
                {professional.diplomes.map((diplome, index) => (
                  <motion.div
                    key={diplome.id || index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100 hover:shadow-md transition-shadow"
                  >
                    <h4 className="font-semibold text-gray-800 mb-1">
                      {diplome.nom_diplome}
                    </h4>
                    <p className="text-meddoc-primary font-medium mb-1">
                      {diplome.etablissement}
                    </p>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <Calendar className="h-3 w-3" />
                      <span>{diplome.date_obtention}</span>
                    </div>
                    {diplome.description && (
                      <p className="text-sm text-gray-600">
                        {diplome.description}
                      </p>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Expériences */}
          {professional?.experiences && professional.experiences.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <Briefcase className="h-4 w-4 text-meddoc-primary" />
                Expériences professionnelles
              </h3>
              <div className="space-y-4">
                {professional.experiences.map((experience, index) => (
                  <motion.div
                    key={experience.id || index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-100 hover:shadow-md transition-shadow"
                  >
                    <h4 className="font-semibold text-gray-800 mb-1">
                      {experience.poste}
                    </h4>
                    <p className="text-meddoc-primary font-medium mb-1">
                      {experience.etablissement}
                    </p>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <Calendar className="h-3 w-3" />
                      <span>
                        {experience.date_debut} -{" "}
                        {experience.est_actuel
                          ? "Aujourd'hui"
                          : experience.date_fin}
                      </span>
                      {experience.est_actuel && (
                        <span className="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs">
                          En cours
                        </span>
                      )}
                    </div>
                    {experience.description && (
                      <p className="text-sm text-gray-600">
                        {experience.description}
                      </p>
                    )}
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Publications */}
          {professional?.publications &&
            professional.publications.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  <FileText className="h-4 w-4 text-meddoc-primary" />
                  Publications et travaux
                </h3>
                <div className="space-y-4">
                  {professional.publications.map((publication, index) => (
                    <motion.div
                      key={publication.id || index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-100 hover:shadow-md transition-shadow"
                    >
                      <h4 className="font-semibold text-gray-800 mb-1">
                        {publication.titre}
                      </h4>
                      <p className="text-meddoc-primary font-medium mb-1">
                        {publication.auteurs}
                      </p>
                      <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                        <Calendar className="h-3 w-3" />
                        <span>{publication.date_publication}</span>
                      </div>
                      {publication.description && (
                        <p className="text-sm text-gray-600 mb-2">
                          {publication.description}
                        </p>
                      )}
                      {publication.lien && (
                        <a
                          href={publication.lien}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-1 text-sm text-meddoc-primary hover:text-meddoc-secondary transition-colors"
                        >
                          <ExternalLink className="h-3 w-3" />
                          Voir la publication
                        </a>
                      )}
                    </motion.div>
                  ))}
                </div>
              </div>
            )}
        </div>
      </div>
    </motion.section>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const CredentialsSection = memo(CredentialsSectionComponent);
