import { FC, memo, useEffect, useRef, useMemo } from 'react';
import { format, isToday, isYesterday, isThisWeek } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Check, CheckCheck, XCircle, Clock } from 'lucide-react';
import { Message } from '@/presentation/types/message.types';
import { TEXT_STYLES } from '@/presentation/styles/common';
import {
  MESSAGE_BUBBLE_STYLES,
  MESSAGE_STATUS_STYLES,
  MESSAGE_TIME_FORMATS,
  PLACEHOLDER_TEXTS,
} from '@/presentation/constants/message.constants';
import MessageItem from './MessageItem';

interface MessageListProps {
  /** Array of messages to display */
  messages: Message[];
  /** ID of the current user */
  currentUserId: string;
  /** Loading state */
  loading?: boolean;
  /** Whether to show message status indicators */
  showStatus?: boolean;
  /** Whether to group messages by date */
  groupByDate?: boolean;
  /** Callback when message is clicked */
  onMessageClick?: (message: Message) => void;
  /** Callback when message status is clicked */
  onStatusClick?: (message: Message) => void;
}

/**
 * MessageList component - Displays a list of messages in a conversation
 * Features auto-scrolling, date grouping, and message status indicators
 * 
 * @param props - Component props
 * @returns {JSX.Element} The message list component
 */
const MessageList: FC<MessageListProps> = memo(({
  messages,
  currentUserId,
  loading = false,
  showStatus = true,
  groupByDate = true,
  onMessageClick,
  onStatusClick,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  /**
   * Scroll to bottom of message list
   */
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'end',
    });
  };

  /**
   * Auto-scroll to bottom when new messages arrive
   */
  useEffect(() => {
    if (messages.length > 0) {
      // Small delay to ensure DOM is updated
      setTimeout(scrollToBottom, 100);
    }
  }, [messages.length]);

  /**
   * Format message time based on when it was sent
   * @param date - Message date
   * @returns {string} Formatted time string
   */
  const formatMessageTime = (date: Date): string => {
    if (isToday(date)) {
      return format(date, 'HH:mm', { locale: fr });
    } else if (isYesterday(date)) {
      return `Hier ${format(date, 'HH:mm', { locale: fr })}`;
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE HH:mm', { locale: fr });
    } else {
      return format(date, 'dd/MM/yyyy HH:mm', { locale: fr });
    }
  };

  /**
   * Format date for date separators
   * @param date - Date to format
   * @returns {string} Formatted date string
   */
  const formatDateSeparator = (date: Date): string => {
    if (isToday(date)) {
      return "Aujourd'hui";
    } else if (isYesterday(date)) {
      return 'Hier';
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE', { locale: fr });
    } else {
      return format(date, 'dd MMMM yyyy', { locale: fr });
    }
  };

  /**
   * Group messages by date for display
   */
  const groupedMessages = useMemo(() => {
    if (!groupByDate) {
      return [{ date: null, messages }];
    }

    const groups: { date: Date | null; messages: Message[] }[] = [];
    let currentDate: string | null = null;
    let currentGroup: Message[] = [];

    messages.forEach((message) => {
      const messageDate = format(message.sentAt, 'yyyy-MM-dd');
      
      if (messageDate !== currentDate) {
        if (currentGroup.length > 0) {
          groups.push({ date: new Date(currentDate!), messages: currentGroup });
        }
        currentDate = messageDate;
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
    });

    if (currentGroup.length > 0) {
      groups.push({ date: currentDate ? new Date(currentDate) : null, messages: currentGroup });
    }

    return groups;
  }, [messages, groupByDate]);

  /**
   * Render message status icon
   * @param status - Message status
   * @returns {JSX.Element} Status icon component
   */
  const renderStatusIcon = (status: Message['status']) => {
    const statusConfig = MESSAGE_STATUS_STYLES[status];
    
    switch (status) {
      case 'sent':
        return <Check className={`h-4 w-4 ${statusConfig.color}`} />;
      case 'delivered':
        return <CheckCheck className={`h-4 w-4 ${statusConfig.color}`} />;
      case 'read':
        return <CheckCheck className={`h-4 w-4 ${statusConfig.color}`} />;
      case 'failed':
        return <XCircle className={`h-4 w-4 ${statusConfig.color}`} />;
      default:
        return <Clock className={`h-4 w-4 ${statusConfig.color}`} />;
    }
  };

  /**
   * Check if message is from current user
   * @param message - Message to check
   * @returns {boolean} Whether message is from current user
   */
  const isOwnMessage = (message: Message): boolean => {
    return message.senderId === currentUserId || message.isOwn === true;
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="flex flex-col items-center gap-3">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-meddoc-primary"></div>
          <p className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
            Chargement des messages...
          </p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <Clock className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className={`text-lg font-medium ${TEXT_STYLES.primary} mb-2`}>
            {PLACEHOLDER_TEXTS.noMessages}
          </h3>
          <p className={`${TEXT_STYLES.secondary}`}>
            Commencez la conversation en envoyant un message
          </p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="flex-1 overflow-y-auto p-4 space-y-4"
      style={{ scrollBehavior: 'smooth' }}
    >
      {groupedMessages.map((group, groupIndex) => (
        <div key={groupIndex} className="space-y-4">
          {/* Date Separator */}
          {group.date && groupByDate && (
            <div className="flex items-center justify-center">
              <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full">
                <span className={`${TEXT_STYLES.small} ${TEXT_STYLES.secondary}`}>
                  {formatDateSeparator(group.date)}
                </span>
              </div>
            </div>
          )}

          {/* Messages in this date group */}
          <div className="space-y-3">
            {group.messages.map((message, messageIndex) => {
              const isOwn = isOwnMessage(message);
              const showAvatar = !isOwn && (
                messageIndex === 0 || 
                group.messages[messageIndex - 1]?.senderId !== message.senderId
              );

              return (
                <MessageItem
                  key={message.id}
                  message={message}
                  isOwn={isOwn}
                  showAvatar={showAvatar}
                  showStatus={showStatus && isOwn}
                  formatTime={formatMessageTime}
                  renderStatusIcon={renderStatusIcon}
                  onClick={onMessageClick}
                  onStatusClick={onStatusClick}
                />
              );
            })}
          </div>
        </div>
      ))}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
});

MessageList.displayName = 'MessageList';

export default MessageList;
