import { FC, memo } from 'react';
import { Send, Paperclip, X, Image, FileText, File } from 'lucide-react';
import { TEXT_STYLES, BUTTON_STYLES } from '@/presentation/styles/common';
import { useMessageComposition } from '@/presentation/hooks/messaging/useMessageComposition';
import {
  PLACEHOLDER_TEXTS,
  MESSAGE_LIMITS,
  ACCESSIBILITY_LABELS,
} from '@/presentation/constants/message.constants';

interface MessageComposerProps {
  /** Callback function to send message */
  onSendMessage: (content: string, attachments?: File[]) => Promise<void>;
  /** Whether the composer is disabled */
  disabled?: boolean;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Whether to show attachment button */
  showAttachments?: boolean;
  /** Maximum number of rows for the textarea */
  maxRows?: number;
}

/**
 * MessageComposer component - Handles message composition with text and attachments
 * Features auto-resize textarea, file attachments, and keyboard shortcuts
 * 
 * @param props - Component props
 * @returns {JSX.Element} The message composer component
 */
const MessageComposer: FC<MessageComposerProps> = memo(({
  onSendMessage,
  disabled = false,
  placeholder = PLACEHOLDER_TEXTS.messageInput,
  showAttachments = true,
  maxRows = 4,
}) => {
  const {
    state,
    setContent,
    addAttachment,
    removeAttachment,
    sendMessage,
    handleKeyDown,
    handleFileInputChange,
    triggerFileSelection,
    formatFileSize,
    getFileIcon,
    canSendMessage,
    fileInputRef,
  } = useMessageComposition(onSendMessage);

  /**
   * Get icon component for file type
   */
  const getFileIconComponent = (fileType: string) => {
    const iconName = getFileIcon(fileType);
    
    switch (iconName) {
      case 'image':
        return <Image className="h-4 w-4" />;
      case 'file-text':
        return <FileText className="h-4 w-4" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  /**
   * Calculate textarea rows based on content
   */
  const calculateRows = (): number => {
    const lines = state.content.split('\n').length;
    return Math.min(Math.max(lines, 1), maxRows);
  };

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
      {/* Attachments Preview */}
      {state.attachments.length > 0 && (
        <div className="mb-3">
          <div className="flex flex-wrap gap-2">
            {state.attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-2 max-w-xs"
              >
                <div className="flex-shrink-0 text-gray-500 dark:text-gray-400">
                  {getFileIconComponent(file.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className={`${TEXT_STYLES.small} font-medium truncate`}>
                    {file.name}
                  </p>
                  <p className={`text-xs ${TEXT_STYLES.secondary}`}>
                    {formatFileSize(file.size)}
                  </p>
                </div>
                <button
                  onClick={() => removeAttachment(index)}
                  className="flex-shrink-0 p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors"
                  aria-label="Supprimer la pièce jointe"
                >
                  <X className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Message */}
      {state.error && (
        <div className="mb-3 p-2 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-sm text-red-600 dark:text-red-400">
            {state.error}
          </p>
        </div>
      )}

      {/* Message Input Area */}
      <div className="flex items-end gap-3">
        {/* Attachment Button */}
        {showAttachments && (
          <button
            onClick={triggerFileSelection}
            disabled={disabled || state.isSending}
            className={`flex-shrink-0 p-2 rounded-lg transition-colors ${
              disabled || state.isSending
                ? 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            aria-label={ACCESSIBILITY_LABELS.attachFile}
          >
            <Paperclip className="h-5 w-5" />
          </button>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileInputChange}
          className="hidden"
          accept="image/*,.pdf,.doc,.docx,.txt"
        />

        {/* Message Input */}
        <div className="flex-1 relative">
          <textarea
            value={state.content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled || state.isSending}
            rows={calculateRows()}
            className={`w-full resize-none rounded-lg border border-gray-200 dark:border-gray-600 
                       bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                       px-4 py-2 pr-12 focus:border-meddoc-primary focus:ring-2 
                       focus:ring-meddoc-primary/20 focus:outline-none transition-colors
                       ${disabled || state.isSending ? 'opacity-50 cursor-not-allowed' : ''}
                       ${state.error ? 'border-red-300 dark:border-red-600' : ''}`}
            style={{ minHeight: '44px' }}
          />
          
          {/* Character Count */}
          {state.content.length > MESSAGE_LIMITS.maxLength * 0.8 && (
            <div className="absolute bottom-2 right-12 text-xs text-gray-400">
              {state.content.length}/{MESSAGE_LIMITS.maxLength}
            </div>
          )}
        </div>

        {/* Send Button */}
        <button
          onClick={sendMessage}
          disabled={!canSendMessage() || disabled}
          className={`flex-shrink-0 p-2 rounded-lg transition-all duration-200 ${
            canSendMessage() && !disabled
              ? `${BUTTON_STYLES.primary} hover:scale-105 active:scale-95`
              : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
          }`}
          aria-label={ACCESSIBILITY_LABELS.sendMessage}
        >
          {state.isSending ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current" />
          ) : (
            <Send className="h-5 w-5" />
          )}
        </button>
      </div>

      {/* Keyboard Shortcuts Hint */}
      <div className="mt-2 flex justify-between items-center">
        <p className={`text-xs ${TEXT_STYLES.secondary}`}>
          <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">
            Entrée
          </kbd>{' '}
          pour envoyer,{' '}
          <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">
            Maj + Entrée
          </kbd>{' '}
          pour une nouvelle ligne
        </p>
        
        {/* Attachment Count */}
        {state.attachments.length > 0 && (
          <p className={`text-xs ${TEXT_STYLES.secondary}`}>
            {state.attachments.length}/{MESSAGE_LIMITS.maxAttachments} pièce(s) jointe(s)
          </p>
        )}
      </div>
    </div>
  );
});

MessageComposer.displayName = 'MessageComposer';

export default MessageComposer;
