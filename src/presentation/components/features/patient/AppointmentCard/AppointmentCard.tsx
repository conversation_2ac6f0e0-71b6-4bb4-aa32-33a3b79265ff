import React from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Box,
  styled,
  Divider,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>,
  Button,
} from "@mui/material";
import {
  AccessTime as AccessTimeIcon,
  Place as PlaceIcon,
  Info as InfoIcon,
  Healing as HealingIcon,
  Event as EventIcon,
  EventBusy as EventBusyIcon,
} from "@mui/icons-material";
import { DESTRUCTIVE, PRIMARY } from "@/shared/constants/Color";
import { rendez_vous_statut_enum } from "@/domain/models/enums";
import { useConsultationState } from "@/presentation/hooks/use-consultation-state";

interface AppointmentCardProps {
  id: number;
  date_rendez_vous: string;
  time: string;
  professional: {
    id: number;
    nom: string;
    prenom: string;
    titre: string;
    raison_sociale: string;
    adresse: string;
    fokontany: string;
    commune: string;
    avatar: string;
  };
  accessInfo?: string;
  patient_id: number;
  statut: rendez_vous_statut_enum;
  motif: string;
  raison: string;
  isPreview?: boolean;
  onClose?: () => void;
}

const StyledCard = styled(Card)(({ theme }) => ({
  border: "1px solid",
  borderColor: theme.palette.divider,
  boxShadow: "none",
  borderRadius: theme.spacing(0.5),
}));

const HeaderBox = styled(Box)<{ type: rendez_vous_statut_enum }>(
  ({ theme, type }) => ({
    display: "flex",
    alignItems: "center",
    padding: theme.spacing(1, 2),
    backgroundColor:
      type === rendez_vous_statut_enum.ANNULER ? DESTRUCTIVE : PRIMARY,
    color: "white",
  })
);

const AppointmentCard: React.FC<AppointmentCardProps> = ({
  id,
  date_rendez_vous,
  time,
  professional,
  accessInfo,
  statut,
  isPreview,
  onClose,
}) => {
  const { loading, handleCancelAppointment } = useConsultationState();
  const onCancelAppointment = async () => {
    await handleCancelAppointment(id);
    onClose();
  };
  return (
    <StyledCard>
      <HeaderBox type={statut}>
        <EventIcon sx={{ mr: 1 }} />
        <Typography variant="subtitle2">{date_rendez_vous}</Typography>
        <AccessTimeIcon sx={{ ml: 2, mr: 1 }} />
        <Typography variant="subtitle2">{time}</Typography>
      </HeaderBox>

      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar
            src={professional.avatar}
            alt={professional.nom}
            sx={{ width: 56, height: 56, mr: 2 }}
          />
          <Box>
            <Typography variant="h6" component="div">
              {`${professional.nom} ${professional.prenom}`}
            </Typography>
            <Typography color="textSecondary" variant="body2">
              {professional.titre}
            </Typography>
          </Box>
        </Box>

        {!isPreview && (
          <>
            <Typography variant="subtitle1" gutterBottom>
              À propos de votre rendez-vous
            </Typography>
            <Box display="flex" alignItems="flex-start" mb={1}>
              <EventIcon sx={{ mr: 1, color: "text.secondary" }} />
              <Typography variant="body2" color="textSecondary" gutterBottom>
                {date_rendez_vous} à {time}
              </Typography>
            </Box>
            <Box display="flex" alignItems="flex-start" mb={1}>
              <HealingIcon sx={{ mr: 1, color: "text.secondary" }} />
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Première consultation
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Typography variant="subtitle1" gutterBottom>
              Informations pratiques
            </Typography>

            {location && (
              <Box display="flex" alignItems="flex-start" mb={1}>
                <PlaceIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Typography variant="body2" color="textSecondary">
                  {professional.raison_sociale}
                  <br />
                  {professional.adresse}
                  <br />
                  {professional.fokontany} {professional.commune}
                </Typography>
              </Box>
            )}

            {accessInfo && (
              <Box display="flex" alignItems="flex-start" mb={2}>
                <InfoIcon sx={{ mr: 1, color: "text.secondary" }} />
                <Typography variant="body2" color="textSecondary">
                  {accessInfo}
                </Typography>
              </Box>
            )}

            <Box display="flex" justifyContent="space-between" mt={2}>
              {handleCancelAppointment &&
                statut === rendez_vous_statut_enum.A_VENIR && (
                  <Button
                    variant="contained"
                    color="error"
                    onClick={onCancelAppointment}
                    sx={{ textTransform: "none" }}
                  >
                    <EventBusyIcon sx={{ mr: 1 }} />
                    {loading
                      ? "Annulation en cours..."
                      : "Annuler le rendez-vous"}
                  </Button>
                )}
            </Box>
          </>
        )}
      </CardContent>
    </StyledCard>
  );
};

export default AppointmentCard;
