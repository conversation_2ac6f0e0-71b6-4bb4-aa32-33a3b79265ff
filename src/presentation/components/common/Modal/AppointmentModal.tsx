import { AppointmentPatientDTO } from "@/domain/DTOS/AppointmentPatientDTO";
import AppointmentCard from "@/presentation/components/features/patient/AppointmentCard/AppointmentCard";
import {
  Dialog,
  DialogTitle,
  IconButton,
  DialogContentText,
} from "@mui/material";
import { X } from "lucide-react";

interface AppointmentModalProps {
  open: boolean;
  onClose: () => void;
  selectedAppointment?: AppointmentPatientDTO;
}

const AppointmentModal = ({
  open,
  onClose,
  selectedAppointment,
}: AppointmentModalProps) => {
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm">
      <DialogContentText>
        <AppointmentCard {...selectedAppointment} onClose={onClose} />
      </DialogContentText>
    </Dialog>
  );
};

export default AppointmentModal;
