import { useRef, Suspense, lazy } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import UnathenticatedLayout from "@/presentation/components/layouts/UnauthenticatedLayout";
import { ProfileHeader } from "@/presentation/components/features/professional/profile/ProfileHeader";
import { NavigationTabs } from "@/presentation/components/features/professional/profile/NavigationTabs";
import { ExpertiseSection } from "@/presentation/components/features/professional/profile/sections/ExpertiseSection";
import { Summary } from "@/presentation/components/features/professional/profile/Summary";
import { useProfileNavigation } from "@/presentation/hooks/useProfileNavigation";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { useProfileData } from "@/presentation/hooks/useProfileData";

// Lazy load heavy components to improve initial page load
const MapSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/MapSection"
  ).then((module) => ({ default: module.MapSection }))
);
const PresentationSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/PresentationSection"
  ).then((module) => ({ default: module.PresentationSection }))
);
const ScheduleSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/ScheduleSection"
  ).then((module) => ({ default: module.ScheduleSection }))
);
const CredentialsSection = lazy(() =>
  import(
    "@/presentation/components/features/professional/profile/sections/CredentialsSection"
  ).then((module) => ({ default: module.CredentialsSection }))
);

const Profile = () => {
  const { id } = useParams();
  const professionalId = id ? parseInt(id) : null;

  // Utiliser notre nouveau hook personnalisé pour les données du profil
  const { profileData, loading, error } = useProfileData(professionalId);

  const stickyRef = useRef<HTMLDivElement>(null);
  const stickyResumeRef = useRef<HTMLDivElement>(null);
  const { activeTab, handleTabClick } = useProfileNavigation(stickyRef);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <UnathenticatedLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-red-600 mb-4">Erreur</h2>
            <p className="text-gray-600">{error}</p>
          </div>
        </div>
      </UnathenticatedLayout>
    );
  }

  return (
    <UnathenticatedLayout>
      {/* Hero Section avec design premium inspiré de la page d'accueil */}
      <div className="relative overflow-hidden bg-gradient-to-br from-meddoc-fonce via-meddoc-fonce/90 to-meddoc-fonce">
        {/* Éléments décoratifs optimisés */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 opacity-5 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJ3aGl0ZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMiIvPjwvZz48L3N2Zz4=')]"></div>
          <div className="absolute top-[-10%] left-[-5%] w-[40%] h-[40%] rounded-full bg-meddoc-primary/20 blur-[120px]"></div>
          <div className="absolute bottom-[-15%] right-[-10%] w-[50%] h-[50%] rounded-full bg-meddoc-secondary/15 blur-[150px]"></div>
        </div>

        <ProfileHeader professional={profileData} />
      </div>

      <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <NavigationTabs
          activeTab={activeTab}
          onTabClick={handleTabClick}
          stickyRef={stickyRef}
        />
      </div>

      {/* Contenu principal avec animations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative bg-gradient-to-br from-slate-50 to-slate-100 min-h-screen"
      >
        {/* Éléments décoratifs de fond */}
        <div className="absolute top-10 right-10 w-32 h-32 rounded-full bg-meddoc-primary/5 z-0"></div>
        <div className="absolute bottom-10 left-10 w-40 h-40 rounded-full bg-meddoc-secondary/5 z-0"></div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-12 pt-8 relative z-10">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2 space-y-8"
          >
            <ExpertiseSection professional={profileData} />

            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-32"></div>
              }
            >
              <CredentialsSection professional={profileData} />
            </Suspense>

            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-64"></div>
              }
            >
              <MapSection professional={profileData} />
            </Suspense>

            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-48"></div>
              }
            >
              <PresentationSection professional={profileData} />
            </Suspense>

            <Suspense
              fallback={
                <div className="bg-white p-6 rounded-2xl shadow-xl border border-gray-100 animate-pulse h-40"></div>
              }
            >
              <ScheduleSection
                horaire_hebdomadaire={profileData?.horaire_hebdomadaire}
              />
            </Suspense>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-1"
          >
            <Summary
              professional={profileData}
              stickyResumeRef={stickyResumeRef}
            />
          </motion.div>
        </div>
      </motion.div>
    </UnathenticatedLayout>
  );
};

export default Profile;
