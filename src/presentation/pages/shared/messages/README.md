# Messages Feature

This module provides a complete messaging interface for the MEDDoC application, allowing patients and professionals to communicate seamlessly.

## Features

### Core Functionality
- **Real-time messaging** - Send and receive text messages
- **File attachments** - Support for images, documents, and other files
- **Conversation management** - Create, archive, and delete conversations
- **Contact selection** - Browse and select contacts to start new conversations
- **Message status** - Track message delivery and read status
- **Search functionality** - Search through conversations and messages

### User Experience
- **Responsive design** - Works on desktop, tablet, and mobile devices
- **Dark mode support** - Consistent with application theme
- **Keyboard shortcuts** - Quick actions for power users
- **Real-time updates** - Live conversation updates
- **Typing indicators** - See when others are typing
- **Online status** - View contact availability

## Architecture

### Components Structure
```
messaging/
├── MessageList.tsx           # Display conversation messages
├── MessageItem.tsx          # Individual message component
├── MessageComposer.tsx      # Message input and composition
├── ConversationHeader.tsx   # Conversation info and actions
└── ContactSelector.tsx      # Contact selection modal
```

### Hooks
```
hooks/messaging/
├── useMessagingData.ts      # Main data management hook
└── useMessageComposition.ts # Message composition logic
```

### Types and Constants
```
types/
└── message.types.ts         # TypeScript interfaces

constants/
└── message.constants.ts     # Styling and configuration constants
```

## Usage

### Basic Implementation
```tsx
import Messages from '@/presentation/pages/shared/messages/Messages';

// Use in your route
<Route path="/messages" element={<Messages />} />
```

### Custom Hook Usage
```tsx
import { useMessagingData } from '@/presentation/hooks/messaging/useMessagingData';

const MyComponent = () => {
  const {
    conversations,
    activeConversation,
    messages,
    sendMessage,
    setActiveConversation
  } = useMessagingData();

  // Your component logic
};
```

## Styling

The messaging interface follows the established design patterns:

- **Colors**: Uses `meddoc-primary` and standard gray palette
- **Typography**: Follows `TEXT_STYLES` constants
- **Cards**: Uses `CARD_STYLES` for consistent containers
- **Buttons**: Implements `BUTTON_STYLES` patterns
- **Dark Mode**: Full support with appropriate color schemes

## Configuration

### File Upload Limits
- **Maximum file size**: 5MB
- **Allowed types**: Images, PDFs, Word documents, text files
- **Maximum attachments**: 5 per message

### Message Limits
- **Maximum length**: 2000 characters
- **Typing timeout**: 3 seconds

### Pagination
- **Messages per page**: 50
- **Conversations per page**: 20

## API Integration

The current implementation uses mock data for development. To integrate with a real backend:

1. **Replace mock data** in `useMessagingData.ts`
2. **Implement API calls** for:
   - `loadConversations()`
   - `loadMessages(conversationId)`
   - `sendMessage(conversationId, content, attachments)`
   - `markAsRead(conversationId)`
   - `createNewConversation(contactId)`

3. **Add real-time updates** using WebSockets or Server-Sent Events

### Example API Integration
```tsx
// Replace mock implementation
const loadConversations = useCallback(async () => {
  setLoading(true);
  try {
    const response = await conversationAPI.getByUserId(currentUser.id);
    setConversations(response.data);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
}, [currentUser?.id]);
```

## Accessibility

The messaging interface includes:

- **ARIA labels** for all interactive elements
- **Keyboard navigation** support
- **Screen reader** compatibility
- **Focus management** for modals and dropdowns
- **High contrast** support in dark mode

## Performance Optimizations

- **React.memo** for component optimization
- **useCallback** for function memoization
- **useMemo** for expensive calculations
- **Virtual scrolling** ready for large message lists
- **Lazy loading** for message history

## Testing Recommendations

### Unit Tests
- Test message composition logic
- Validate file upload restrictions
- Test search and filtering functionality
- Verify keyboard shortcuts

### Integration Tests
- Test conversation flow
- Verify real-time updates
- Test file attachment handling
- Validate responsive design

### E2E Tests
- Complete messaging workflow
- Cross-device compatibility
- Performance under load
- Accessibility compliance

## Future Enhancements

### Planned Features
- **Voice messages** - Audio recording and playback
- **Video calls** - Integrated video communication
- **Message reactions** - Emoji reactions to messages
- **Message forwarding** - Share messages between conversations
- **Message scheduling** - Send messages at specific times
- **Group conversations** - Multi-participant messaging
- **Message encryption** - End-to-end encryption for security

### Technical Improvements
- **Offline support** - Cache messages for offline viewing
- **Push notifications** - Real-time notifications
- **Message search** - Full-text search across all messages
- **Message translation** - Multi-language support
- **Advanced file preview** - In-app document viewing

## Troubleshooting

### Common Issues

1. **Messages not loading**
   - Check network connectivity
   - Verify API endpoints
   - Check authentication status

2. **File upload failures**
   - Verify file size limits
   - Check allowed file types
   - Ensure proper permissions

3. **Real-time updates not working**
   - Check WebSocket connection
   - Verify event listeners
   - Test network stability

### Debug Mode
Enable debug logging by setting:
```tsx
const DEBUG_MESSAGING = process.env.NODE_ENV === 'development';
```

## Contributing

When contributing to the messaging feature:

1. **Follow TypeScript** strict typing
2. **Add JSDoc comments** for all functions
3. **Include unit tests** for new functionality
4. **Update this README** for significant changes
5. **Test accessibility** features
6. **Verify responsive design** on all devices

## Dependencies

The messaging feature relies on:

- **React** 18+ with hooks
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **date-fns** for date formatting
- **Redux Toolkit** for state management
