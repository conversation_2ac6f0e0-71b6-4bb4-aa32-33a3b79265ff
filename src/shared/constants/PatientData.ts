import { Patient } from "@/domain/models";
import { patients_groupe_sanguin_enum, sexe_enum } from "@/domain/models/enums";

export const PatientData: Patient[] = [
    {
        id: 1,
        utilisateur_id: 1,
        unique_id: "Patient 1",
        nom: "<PERSON>",
        prenom: "Doe",
        sexe: sexe_enum.homme,
        date_naissance: new Date(),
        adresse: "123 Main St",
        district: "District 1",
        commune: "Commune 1",
        fokontany: "Fokontany 1",
        groupe_sanguin: patients_groupe_sanguin_enum["A+"],
        nationalite: "Malagasy",
        decede: false,
        pays: "Madagascar",
        situation_matrimonial: "<PERSON>",
        nb_enfant: 2,
        profession: "Profession 1"
    },
    {
        id: 2,
        utilisateur_id: 2,
        unique_id: "Patient 2",
        nom: "<PERSON>",
        prenom: "<PERSON>",
        sexe: sexe_enum.femme,
        date_naissance: new Date(),
        adresse: "456 Elm St",
        district: "District 2",
        commune: "Commune 2",
        fokontany: "Fokontany 2",
        groupe_sanguin: patients_groupe_sanguin_enum["B+"],
        nationalite: "Malagasy",
        decede: false,
        pays: "Madagascar",
        situation_matrimonial: "Marie",
        nb_enfant: 1,
        profession: "Profession 2"
    },
    {
        id: 3,
        utilisateur_id: 3,
        unique_id: "Patient 3",
        nom: "Bob",
        prenom: "Johnson",
        sexe: sexe_enum.homme,
        date_naissance: new Date(),
        adresse: "789 Oak St",
        district: "District 3",
        commune: "Commune 3",
        fokontany: "Fokontany 3",
        groupe_sanguin: patients_groupe_sanguin_enum["O+"],
        nationalite: "Malagasy",
        decede: false,
        pays: "Madagascar",
        situation_matrimonial: "Marie",
        nb_enfant: 0,
        profession: "Profession 3"
    }
];